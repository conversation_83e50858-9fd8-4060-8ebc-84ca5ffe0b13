-- ================================
-- 完整的系统初始化脚本（最终版）
-- 包含：表结构创建 + 权限数据初始化 + 测试数据
-- 注意：此脚本会清空所有现有数据，请谨慎使用！
-- ================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ================================
-- 1. 清空现有数据
-- ================================

-- 清空关联表数据
DROP TABLE IF EXISTS `sys_role_permission`;
DROP TABLE IF EXISTS `sys_user_role`;

-- 清空主表数据
DROP TABLE IF EXISTS `sys_permission`;
DROP TABLE IF EXISTS `sys_role`;
DROP TABLE IF EXISTS `SYS_MENU_ENTITY`;
DROP TABLE IF EXISTS `sys_dept`;
DROP TABLE IF EXISTS `sys_user`;

-- ================================
-- 2. 创建表结构
-- ================================

-- 系统用户表
CREATE TABLE `sys_user`
(
    `id`                   bigint       NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username`             varchar(50)  NOT NULL COMMENT '用户名',
    `password`             varchar(100) NOT NULL COMMENT '密码',
    `real_name`            varchar(50)  NOT NULL COMMENT '真实姓名',
    `nickname`             varchar(50)  DEFAULT NULL COMMENT '昵称',
    `email`                varchar(100) DEFAULT NULL COMMENT '邮箱',
    `phone`                varchar(20)  DEFAULT NULL COMMENT '手机号',
    `gender`               varchar(10)  DEFAULT 'U' COMMENT '性别：M-男，F-女，U-未知',
    `avatar`               varchar(255) DEFAULT NULL COMMENT '头像URL',
    `dept_id`              bigint       DEFAULT NULL COMMENT '部门ID',
    `position`             varchar(50)  DEFAULT NULL COMMENT '职位',
    `status`               varchar(20)  DEFAULT 'NORMAL' COMMENT '状态：NORMAL-正常，DISABLED-禁用，LOCKED-锁定',
    `builtin_flag`         varchar(1)   DEFAULT 'N' COMMENT '内置标识：Y-是，N-否',
    `last_login_time`      datetime     DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip`        varchar(50)  DEFAULT NULL COMMENT '最后登录IP',
    `lock_time`            datetime     DEFAULT NULL COMMENT '账户锁定时间',
    `password_expire_time` datetime     DEFAULT NULL COMMENT '密码过期时间',
    `account_expire_time`  datetime     DEFAULT NULL COMMENT '账户过期时间',
    `remark`               varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time`          datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`            bigint       DEFAULT NULL COMMENT '创建人ID',
    `update_by`            bigint       DEFAULT NULL COMMENT '更新人ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    KEY `idx_dept_id` (`dept_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_by` (`create_by`),
    KEY `idx_update_by` (`update_by`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='系统用户表';

-- 系统角色表
CREATE TABLE `sys_role`
(
    `id`           bigint      NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `role_code`    varchar(50) NOT NULL COMMENT '角色编码',
    `role_name`    varchar(50) NOT NULL COMMENT '角色名称',
    `description`  varchar(200) DEFAULT NULL COMMENT '角色描述',
    `sort_order`   int          DEFAULT 0 COMMENT '排序号',
    `status`       varchar(20)  DEFAULT 'NORMAL' COMMENT '状态：NORMAL-正常，DISABLED-禁用',
    `data_scope`   varchar(20)  DEFAULT 'SELF_ONLY' COMMENT '数据权限范围：ALL-全部，DEPT_AND_CHILD-本部门及子部门，DEPT_ONLY-仅本部门，SELF_ONLY-仅本人',
    `builtin_flag` varchar(1)   DEFAULT 'N' COMMENT '内置标识：Y-是，N-否',
    `remark`       varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time`  datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`    bigint       DEFAULT NULL COMMENT '创建人ID',
    `update_by`    bigint       DEFAULT NULL COMMENT '更新人ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_code` (`role_code`),
    KEY `idx_status` (`status`),
    KEY `idx_create_by` (`create_by`),
    KEY `idx_update_by` (`update_by`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='系统角色表';

-- 系统部门表
CREATE TABLE `sys_dept`
(
    `id`          bigint      NOT NULL AUTO_INCREMENT COMMENT '部门ID',
    `parent_id`   bigint       DEFAULT 0 COMMENT '父部门ID',
    `dept_code`   varchar(50) NOT NULL COMMENT '部门编码',
    `dept_name`   varchar(50) NOT NULL COMMENT '部门名称',
    `dept_type`   varchar(20)  DEFAULT 'DEPT' COMMENT '部门类型：COMPANY-公司，DEPT-部门，GROUP-小组',
    `sort_order`  int          DEFAULT 0 COMMENT '显示顺序',
    `leader_id`   bigint       DEFAULT NULL COMMENT '负责人ID',
    `phone`       varchar(20)  DEFAULT NULL COMMENT '联系电话',
    `email`       varchar(100) DEFAULT NULL COMMENT '邮箱',
    `address`     varchar(200) DEFAULT NULL COMMENT '部门地址',
    `status`      varchar(20)  DEFAULT 'NORMAL' COMMENT '状态：NORMAL-正常，DISABLED-禁用',
    `ancestors`   varchar(500) DEFAULT NULL COMMENT '祖级列表（用逗号分隔的父级ID列表）',
    `level`       int          DEFAULT 0 COMMENT '层级深度',
    `remark`      varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   bigint       DEFAULT NULL COMMENT '创建人ID',
    `update_by`   bigint       DEFAULT NULL COMMENT '更新人ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_dept_code` (`dept_code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_status` (`status`),
    KEY `idx_leader_id` (`leader_id`),
    KEY `idx_create_by` (`create_by`),
    KEY `idx_update_by` (`update_by`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='系统部门表';

-- 系统菜单表
CREATE TABLE `SYS_MENU_ENTITY`
(
    `id`          bigint      NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
    `parent_id`   bigint       DEFAULT 0 COMMENT '父菜单ID',
    `menu_name`   varchar(50) NOT NULL COMMENT '菜单名称',
    `title`       varchar(50)  DEFAULT NULL COMMENT '菜单标题',
    `menu_type`   varchar(20)  DEFAULT 'MENU' COMMENT '菜单类型：DIRECTORY-目录，MENU-菜单，BUTTON-按钮',
    `path`        varchar(200) DEFAULT NULL COMMENT '路由地址',
    `component`   varchar(200) DEFAULT NULL COMMENT '组件路径',
    `permission`  varchar(100) DEFAULT NULL COMMENT '权限标识',
    `icon`        varchar(100) DEFAULT NULL COMMENT '菜单图标',
    `sort_order`  int          DEFAULT 0 COMMENT '显示顺序',
    `is_frame`    varchar(1)   DEFAULT 'N' COMMENT '是否外链：Y-是，N-否',
    `is_cache`    varchar(1)   DEFAULT 'N' COMMENT '是否缓存：Y-是，N-否',
    `visible`     varchar(1)   DEFAULT 'Y' COMMENT '是否显示：Y-是，N-否',
    `status`      varchar(20)  DEFAULT 'NORMAL' COMMENT '状态：NORMAL-正常，DISABLED-禁用',
    `remark`      varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   bigint       DEFAULT NULL COMMENT '创建人ID',
    `update_by`   bigint       DEFAULT NULL COMMENT '更新人ID',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_by` (`create_by`),
    KEY `idx_update_by` (`update_by`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='系统菜单表';

-- 系统权限表
CREATE TABLE `sys_permission`
(
    `id`              bigint       NOT NULL AUTO_INCREMENT COMMENT '权限ID',
    `permission_code` varchar(100) NOT NULL COMMENT '权限编码',
    `permission_name` varchar(50)  NOT NULL COMMENT '权限名称',
    `permission_type` varchar(20)  DEFAULT 'API' COMMENT '权限类型：API-接口，MENU-菜单，BUTTON-按钮',
    `resource_path`   varchar(200) DEFAULT NULL COMMENT '资源路径',
    `request_method`  varchar(10)  DEFAULT NULL COMMENT '请求方法：GET,POST,PUT,DELETE',
    `description`     varchar(200) DEFAULT NULL COMMENT '权限描述',
    `sort_order`      int          DEFAULT 0 COMMENT '排序号',
    `status`          varchar(20)  DEFAULT 'NORMAL' COMMENT '状态：NORMAL-正常，DISABLED-禁用',
    `remark`          varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time`     datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`       bigint       DEFAULT NULL COMMENT '创建人ID',
    `update_by`       bigint       DEFAULT NULL COMMENT '更新人ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`permission_code`),
    KEY `idx_permission_type` (`permission_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_by` (`create_by`),
    KEY `idx_update_by` (`update_by`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='系统权限表';

-- 用户角色关联表
CREATE TABLE `sys_user_role`
(
    `id`      bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_role_id` (`role_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='用户角色关联表';

-- 角色权限关联表
CREATE TABLE `sys_role_permission`
(
    `id`            bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `role_id`       bigint NOT NULL COMMENT '角色ID',
    `permission_id` bigint NOT NULL COMMENT '权限ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_permission_id` (`permission_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='角色权限关联表';

-- ================================
-- 3. 插入基础数据
-- ================================

-- 生成雪花算法风格的ID函数（使用时间戳+随机数）
-- 格式：时间戳(13位) + 机器ID(2位) + 随机数(4位) = 19位
DELIMITER $$
CREATE FUNCTION generate_snowflake_id() RETURNS BIGINT
    READS SQL DATA
    DETERMINISTIC
BEGIN
    DECLARE timestamp_part BIGINT;
    DECLARE machine_part INT;
    DECLARE random_part INT;
    DECLARE result_id BIGINT;

    -- 获取当前时间戳（毫秒）
    SET timestamp_part = UNIX_TIMESTAMP(NOW(3)) * 1000 + MICROSECOND(NOW(3)) DIV 1000;

    -- 机器ID（固定为01）
    SET machine_part = 1;

    -- 随机数部分（0-9999）
    SET random_part = FLOOR(RAND() * 10000);

    -- 组合成19位ID：时间戳 + 机器ID + 随机数
    SET result_id = timestamp_part * 100000 + machine_part * 10000 + random_part;

    RETURN result_id;
END$$
DELIMITER ;

-- 插入部门数据（使用生成的雪花算法风格ID）
INSERT INTO `sys_dept` (`id`, `parent_id`, `dept_code`, `dept_name`, `dept_type`, `sort_order`, `leader_id`, `phone`,
                        `email`, `address`, `status`, `ancestors`, `level`, `remark`, `create_by`, `update_by`)
VALUES (generate_snowflake_id(), 0, 'ROOT', '中国联通陕西分公司', 'COMPANY', 1, NULL, '029-12345678',
        '<EMAIL>', '陕西省西安市', 'NORMAL', '0', 1, '根部门', NULL, NULL);

-- 获取根部门ID用于后续插入
SET @root_dept_id = (SELECT id
                     FROM sys_dept
                     WHERE dept_code = 'ROOT');

INSERT INTO `sys_dept` (`id`, `parent_id`, `dept_code`, `dept_name`, `dept_type`, `sort_order`, `leader_id`, `phone`,
                        `email`, `address`, `status`, `ancestors`, `level`, `remark`, `create_by`, `update_by`)
VALUES (generate_snowflake_id(), @root_dept_id, 'TECH', '技术部', 'DEPT', 2, NULL, '029-12345679', '<EMAIL>',
        '陕西省西安市技术大厦', 'NORMAL', CONCAT('0,', @root_dept_id), 2, '技术开发部门', NULL, NULL),
       (generate_snowflake_id(), @root_dept_id, 'MARKET', '市场部', 'DEPT', 3, NULL, '029-12345680',
        '<EMAIL>', '陕西省西安市市场大厦', 'NORMAL', CONCAT('0,', @root_dept_id), 2, '市场营销部门', NULL,
        NULL),
       (generate_snowflake_id(), @root_dept_id, 'FINANCE', '财务部', 'DEPT', 4, NULL, '029-12345681',
        '<EMAIL>', '陕西省西安市财务大厦', 'NORMAL', CONCAT('0,', @root_dept_id), 2, '财务管理部门', NULL,
        NULL);

-- 获取技术部ID用于插入开发组
SET @tech_dept_id = (SELECT id
                     FROM sys_dept
                     WHERE dept_code = 'TECH');

INSERT INTO `sys_dept` (`id`, `parent_id`, `dept_code`, `dept_name`, `dept_type`, `sort_order`, `leader_id`, `phone`,
                        `email`, `address`, `status`, `ancestors`, `level`, `remark`, `create_by`, `update_by`)
VALUES (generate_snowflake_id(), @tech_dept_id, 'DEV', '开发组', 'GROUP', 5, NULL, '029-12345682', '<EMAIL>',
        '陕西省西安市技术大厦8楼', 'NORMAL', CONCAT('0,', @root_dept_id, ',', @tech_dept_id), 3, '软件开发小组', NULL,
        NULL);

-- 插入角色数据（使用生成的雪花算法风格ID）
INSERT INTO `sys_role` (`id`, `role_code`, `role_name`, `description`, `sort_order`, `status`, `data_scope`,
                        `builtin_flag`, `remark`, `create_by`, `update_by`)
VALUES (generate_snowflake_id(), 'SUPER_ADMIN', '超级管理员', '系统超级管理员，拥有所有权限', 1, 'NORMAL', 'ALL', 'Y',
        '系统内置角色，不可删除', NULL, NULL),
       (generate_snowflake_id(), 'ADMIN', '系统管理员', '系统管理员，拥有大部分管理权限', 2, 'NORMAL', 'ALL', 'Y',
        '系统内置角色，不可删除', NULL, NULL),
       (generate_snowflake_id(), 'DEPT_ADMIN', '部门管理员', '部门管理员，可管理本部门及子部门数据', 3, 'NORMAL',
        'DEPT_AND_CHILD', 'N', '部门管理员角色', NULL, NULL),
       (generate_snowflake_id(), 'USER', '普通用户', '普通用户，只能查看和操作自己的数据', 4, 'NORMAL', 'SELF_ONLY', 'N',
        '普通用户角色', NULL, NULL),
       (generate_snowflake_id(), 'OPERATOR', '操作员', '操作员，负责具体业务操作', 5, 'NORMAL', 'DEPT_ONLY', 'N',
        '操作员角色', NULL, NULL);

-- 插入权限数据（使用生成的雪花算法风格ID）
INSERT INTO `sys_permission` (`id`, `permission_code`, `permission_name`, `permission_type`, `resource_path`,
                              `request_method`, `description`, `sort_order`, `status`, `remark`, `create_by`,
                              `update_by`)
VALUES
-- 用户管理权限
(generate_snowflake_id(), 'system:user:view', '查看用户', 'API', '/api/system/user/**', 'GET', '查看用户信息的权限', 1,
 'NORMAL', '用户管理-查看', NULL, NULL),
(generate_snowflake_id(), 'system:user:create', '创建用户', 'API', '/api/system/user/create', 'POST',
 '创建新用户的权限', 2, 'NORMAL', '用户管理-创建', NULL, NULL),
(generate_snowflake_id(), 'system:user:update', '更新用户', 'API', '/api/system/user/update', 'POST',
 '更新用户信息的权限', 3, 'NORMAL', '用户管理-更新', NULL, NULL),
(generate_snowflake_id(), 'system:user:delete', '删除用户', 'API', '/api/system/user/delete', 'POST', '删除用户的权限',
 4, 'NORMAL', '用户管理-删除', NULL, NULL),
(generate_snowflake_id(), 'system:user:reset-password', '重置密码', 'API', '/api/system/user/reset-password', 'POST',
 '重置用户密码的权限', 5, 'NORMAL', '用户管理-重置密码', NULL, NULL),
(generate_snowflake_id(), 'system:user:change-password', '修改密码', 'API', '/api/system/user/change-password', 'POST',
 '修改用户密码的权限', 6, 'NORMAL', '用户管理-修改密码', NULL, NULL),
(generate_snowflake_id(), 'system:user:update-status', '更新用户状态', 'API', '/api/system/user/update-status', 'POST',
 '更新用户状态的权限', 7, 'NORMAL', '用户管理-状态管理', NULL, NULL),
(generate_snowflake_id(), 'system:user:assign-roles', '分配角色', 'API', '/api/system/user/assign-roles', 'POST',
 '为用户分配角色的权限', 8, 'NORMAL', '用户管理-角色分配', NULL, NULL),
(generate_snowflake_id(), 'system:user:lock', '锁定用户', 'API', '/api/system/user/lock', 'POST', '锁定用户的权限', 9,
 'NORMAL', '用户管理-锁定', NULL, NULL),
(generate_snowflake_id(), 'system:user:unlock', '解锁用户', 'API', '/api/system/user/unlock', 'POST', '解锁用户的权限',
 10, 'NORMAL', '用户管理-解锁', NULL, NULL),

-- 角色管理权限
(generate_snowflake_id(), 'system:role:view', '查看角色', 'API', '/api/system/role/**', 'GET', '查看角色信息的权限', 11,
 'NORMAL', '角色管理-查看', NULL, NULL),
(generate_snowflake_id(), 'system:role:create', '创建角色', 'API', '/api/system/role/create', 'POST',
 '创建新角色的权限', 12, 'NORMAL', '角色管理-创建', NULL, NULL),
(generate_snowflake_id(), 'system:role:update', '更新角色', 'API', '/api/system/role/update', 'POST',
 '更新角色信息的权限', 13, 'NORMAL', '角色管理-更新', NULL, NULL),
(generate_snowflake_id(), 'system:role:delete', '删除角色', 'API', '/api/system/role/delete', 'POST', '删除角色的权限',
 14, 'NORMAL', '角色管理-删除', NULL, NULL),
(generate_snowflake_id(), 'system:role:update-status', '更新角色状态', 'API', '/api/system/role/update-status', 'POST',
 '更新角色状态的权限', 15, 'NORMAL', '角色管理-状态管理', NULL, NULL),
(generate_snowflake_id(), 'system:role:assign-permissions', '分配权限', 'API', '/api/system/role/assign-permissions',
 'POST', '为角色分配权限的权限', 16, 'NORMAL', '角色管理-权限分配', NULL, NULL),

-- 部门管理权限
(generate_snowflake_id(), 'system:dept:view', '查看部门', 'API', '/api/system/dept/**', 'GET', '查看部门信息的权限', 17,
 'NORMAL', '部门管理-查看', NULL, NULL),
(generate_snowflake_id(), 'system:dept:create', '创建部门', 'API', '/api/system/dept/create', 'POST',
 '创建新部门的权限', 18, 'NORMAL', '部门管理-创建', NULL, NULL),
(generate_snowflake_id(), 'system:dept:update', '更新部门', 'API', '/api/system/dept/update', 'POST',
 '更新部门信息的权限', 19, 'NORMAL', '部门管理-更新', NULL, NULL),
(generate_snowflake_id(), 'system:dept:delete', '删除部门', 'API', '/api/system/dept/delete', 'POST', '删除部门的权限',
 20, 'NORMAL', '部门管理-删除', NULL, NULL),
(generate_snowflake_id(), 'system:dept:update-status', '更新部门状态', 'API', '/api/system/dept/update-status', 'POST',
 '更新部门状态的权限', 21, 'NORMAL', '部门管理-状态管理', NULL, NULL),

-- 菜单管理权限
(generate_snowflake_id(), 'system:menu:view', '查看菜单', 'API', '/api/system/menu/**', 'GET', '查看菜单信息的权限', 22,
 'NORMAL', '菜单管理-查看', NULL, NULL),
(generate_snowflake_id(), 'system:menu:create', '创建菜单', 'API', '/api/system/menu/create', 'POST',
 '创建新菜单的权限', 23, 'NORMAL', '菜单管理-创建', NULL, NULL),
(generate_snowflake_id(), 'system:menu:update', '更新菜单', 'API', '/api/system/menu/update', 'POST',
 '更新菜单信息的权限', 24, 'NORMAL', '菜单管理-更新', NULL, NULL),
(generate_snowflake_id(), 'system:menu:delete', '删除菜单', 'API', '/api/system/menu/delete', 'POST', '删除菜单的权限',
 25, 'NORMAL', '菜单管理-删除', NULL, NULL),

-- 权限管理权限
(generate_snowflake_id(), 'system:permission:view', '查看权限', 'API', '/api/system/permission/**', 'GET',
 '查看权限信息的权限', 26, 'NORMAL', '权限管理-查看', NULL, NULL),
(generate_snowflake_id(), 'system:permission:create', '创建权限', 'API', '/api/system/permission/create', 'POST',
 '创建新权限的权限', 27, 'NORMAL', '权限管理-创建', NULL, NULL),
(generate_snowflake_id(), 'system:permission:update', '更新权限', 'API', '/api/system/permission/update', 'POST',
 '更新权限信息的权限', 28, 'NORMAL', '权限管理-更新', NULL, NULL),
(generate_snowflake_id(), 'system:permission:delete', '删除权限', 'API', '/api/system/permission/delete', 'POST',
 '删除权限的权限', 29, 'NORMAL', '权限管理-删除', NULL, NULL);

-- 插入用户数据（密码为BCrypt加密的123456，使用生成的雪花算法风格ID）
-- 获取部门ID
SET @root_dept_id = (SELECT id
                     FROM sys_dept
                     WHERE dept_code = 'ROOT');
SET @tech_dept_id = (SELECT id
                     FROM sys_dept
                     WHERE dept_code = 'TECH');
SET @dev_dept_id = (SELECT id
                    FROM sys_dept
                    WHERE dept_code = 'DEV');

INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `nickname`, `email`, `phone`, `gender`, `avatar`,
                        `dept_id`, `position`, `status`, `builtin_flag`, `password_expire_time`, `account_expire_time`,
                        `remark`, `create_by`, `update_by`)
VALUES (generate_snowflake_id(), 'superadmin', '$2a$10$7JB720yubVSOfvVWbazBuOWShWzhlicrXMmcbRFb.a5lIAKVdR00O',
        '超级管理员', '超管', '<EMAIL>', '***********', 'M', NULL, @root_dept_id, '超级管理员',
        'NORMAL', 'Y', DATE_ADD(NOW(), INTERVAL 3 MONTH), DATE_ADD(NOW(), INTERVAL 1 YEAR),
        '系统超级管理员，拥有所有权限', NULL, NULL),
       (generate_snowflake_id(), 'admin', '$2a$10$7JB720yubVSOfvVWbazBuOWShWzhlicrXMmcbRFb.a5lIAKVdR00O', '系统管理员',
        '管理员', '<EMAIL>', '***********', 'M', NULL, @root_dept_id, '系统管理员', 'NORMAL', 'Y',
        DATE_ADD(NOW(), INTERVAL 3 MONTH), DATE_ADD(NOW(), INTERVAL 1 YEAR), '系统管理员，拥有大部分权限', NULL, NULL),
       (generate_snowflake_id(), 'deptadmin', '$2a$10$7JB720yubVSOfvVWbazBuOWShWzhlicrXMmcbRFb.a5lIAKVdR00O',
        '部门管理员', '部门管理员', '<EMAIL>', '13800000002', 'F', NULL, @tech_dept_id, '技术部经理',
        'NORMAL', 'N', DATE_ADD(NOW(), INTERVAL 3 MONTH), DATE_ADD(NOW(), INTERVAL 1 YEAR), '技术部门管理员', NULL,
        NULL),
       (generate_snowflake_id(), 'testuser', '$2a$10$7JB720yubVSOfvVWbazBuOWShWzhlicrXMmcbRFb.a5lIAKVdR00O', '测试用户',
        '测试员', '<EMAIL>', '13800000003', 'M', NULL, @dev_dept_id, '软件工程师', 'NORMAL', 'N',
        DATE_ADD(NOW(), INTERVAL 3 MONTH), DATE_ADD(NOW(), INTERVAL 1 YEAR), '普通测试用户', NULL, NULL);

-- 插入菜单数据（使用生成的雪花算法风格ID）
INSERT INTO `SYS_MENU_ENTITY` (`id`, `parent_id`, `menu_name`, `title`, `menu_type`, `path`, `component`, `permission`, `icon`,
                        `sort_order`, `is_frame`, `is_cache`, `visible`, `status`, `remark`, `create_by`, `update_by`)
VALUES (generate_snowflake_id(), 0, '系统管理', '系统管理', 'DIRECTORY', '/system', NULL, NULL, 'system', 1, 'N', 'N',
        'Y', 'NORMAL', '系统管理目录', NULL, NULL);

-- 获取系统管理目录ID
SET @system_menu_id = (SELECT id
                       FROM SYS_MENU_ENTITY
                       WHERE menu_name = '系统管理'
                         AND parent_id = 0);

INSERT INTO `SYS_MENU_ENTITY` (`id`, `parent_id`, `menu_name`, `title`, `menu_type`, `path`, `component`, `permission`, `icon`,
                        `sort_order`, `is_frame`, `is_cache`, `visible`, `status`, `remark`, `create_by`, `update_by`)
VALUES (generate_snowflake_id(), @system_menu_id, '用户管理', '用户管理', 'MENU', '/system/user', 'system/user/index',
        'system:user:view', 'user', 2, 'N', 'Y', 'Y', 'NORMAL', '用户管理菜单', NULL, NULL),
       (generate_snowflake_id(), @system_menu_id, '角色管理', '角色管理', 'MENU', '/system/role', 'system/role/index',
        'system:role:view', 'role', 3, 'N', 'Y', 'Y', 'NORMAL', '角色管理菜单', NULL, NULL),
       (generate_snowflake_id(), @system_menu_id, '部门管理', '部门管理', 'MENU', '/system/dept', 'system/dept/index',
        'system:dept:view', 'dept', 4, 'N', 'Y', 'Y', 'NORMAL', '部门管理菜单', NULL, NULL),
       (generate_snowflake_id(), @system_menu_id, '菜单管理', '菜单管理', 'MENU', '/system/menu', 'system/menu/index',
        'system:menu:view', 'menu', 5, 'N', 'Y', 'Y', 'NORMAL', '菜单管理菜单', NULL, NULL),
       (generate_snowflake_id(), @system_menu_id, '权限管理', '权限管理', 'MENU', '/system/permission',
        'system/permission/index', 'system:permission:view', 'permission', 6, 'N', 'Y', 'Y', 'NORMAL', '权限管理菜单',
        NULL, NULL);

-- ================================
-- 4. 插入关联数据
-- ================================

-- 插入用户角色关联（使用雪花算法ID）
-- 获取用户ID
SET @superadmin_id = (SELECT id
                      FROM sys_user
                      WHERE username = 'superadmin');
SET @admin_id = (SELECT id
                 FROM sys_user
                 WHERE username = 'admin');
SET @deptadmin_id = (SELECT id
                     FROM sys_user
                     WHERE username = 'deptadmin');
SET @testuser_id = (SELECT id
                    FROM sys_user
                    WHERE username = 'testuser');

-- 获取角色ID
SET @super_admin_role_id = (SELECT id
                            FROM sys_role
                            WHERE role_code = 'SUPER_ADMIN');
SET @admin_role_id = (SELECT id
                      FROM sys_role
                      WHERE role_code = 'ADMIN');
SET @dept_admin_role_id = (SELECT id
                           FROM sys_role
                           WHERE role_code = 'DEPT_ADMIN');
SET @user_role_id = (SELECT id
                     FROM sys_role
                     WHERE role_code = 'USER');

INSERT INTO `sys_user_role` (`id`, `user_id`, `role_id`)
VALUES (generate_snowflake_id(), @superadmin_id, @super_admin_role_id), -- superadmin -> SUPER_ADMIN
       (generate_snowflake_id(), @admin_id, @admin_role_id),            -- admin -> ADMIN
       (generate_snowflake_id(), @deptadmin_id, @dept_admin_role_id),   -- deptadmin -> DEPT_ADMIN
       (generate_snowflake_id(), @testuser_id, @user_role_id);
-- testuser -> USER

-- 插入角色权限关联
-- 系统管理员权限（拥有大部分权限，使用生成的雪花算法风格ID）
-- 使用子查询直接插入，避免过多变量
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`)
SELECT generate_snowflake_id()                             as id,
       (SELECT id FROM sys_role WHERE role_code = 'ADMIN') as role_id,
       p.id                                                as permission_id
FROM sys_permission p
WHERE p.permission_code IN (
    -- 用户管理权限
                            'system:user:view', 'system:user:create', 'system:user:update', 'system:user:delete',
                            'system:user:reset-password', 'system:user:change-password', 'system:user:update-status',
                            'system:user:assign-roles', 'system:user:lock', 'system:user:unlock',
    -- 角色管理权限
                            'system:role:view', 'system:role:create', 'system:role:update', 'system:role:delete',
                            'system:role:update-status', 'system:role:assign-permissions',
    -- 部门管理权限
                            'system:dept:view', 'system:dept:create', 'system:dept:update', 'system:dept:delete',
                            'system:dept:update-status',
    -- 菜单管理权限
                            'system:menu:view', 'system:menu:create', 'system:menu:update', 'system:menu:delete',
    -- 权限管理权限
                            'system:permission:view', 'system:permission:create', 'system:permission:update',
                            'system:permission:delete'
    );

-- 部门管理员权限（部门和用户相关权限）
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`)
SELECT generate_snowflake_id()                                  as id,
       (SELECT id FROM sys_role WHERE role_code = 'DEPT_ADMIN') as role_id,
       p.id                                                     as permission_id
FROM sys_permission p
WHERE p.permission_code IN (
                            'system:user:view', 'system:user:update', 'system:user:update-status',
                            'system:dept:view', 'system:dept:update'
    );

-- 普通用户权限（基本查看权限）
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`)
SELECT generate_snowflake_id()                            as id,
       (SELECT id FROM sys_role WHERE role_code = 'USER') as role_id,
       p.id                                               as permission_id
FROM sys_permission p
WHERE p.permission_code IN (
                            'system:user:view', 'system:role:view', 'system:dept:view', 'system:menu:view'
    );

-- 操作员权限（基本操作权限）
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`)
SELECT generate_snowflake_id()                                as id,
       (SELECT id FROM sys_role WHERE role_code = 'OPERATOR') as role_id,
       p.id                                                   as permission_id
FROM sys_permission p
WHERE p.permission_code IN (
                            'system:user:view', 'system:role:view', 'system:dept:view',
                            'system:user:update', 'system:user:change-password'
    );

-- ================================
-- 5. 重置自增ID（雪花算法不需要重置，注释掉）
-- ================================

-- 使用雪花算法ID，不需要重置自增ID
-- ALTER TABLE `sys_user` AUTO_INCREMENT = 5;
-- ALTER TABLE `sys_role` AUTO_INCREMENT = 6;
-- ALTER TABLE `sys_dept` AUTO_INCREMENT = 6;
-- ALTER TABLE `SYS_MENU_ENTITY` AUTO_INCREMENT = 7;
-- ALTER TABLE `sys_permission` AUTO_INCREMENT = 30;

-- ================================
-- 6. 恢复外键检查
-- ================================

SET FOREIGN_KEY_CHECKS = 1;

-- ================================
-- 7. 验证数据完整性
-- ================================

-- 显示统计信息
SELECT '数据初始化完成'                           as status,
       (SELECT COUNT(*) FROM sys_user)            as total_users,
       (SELECT COUNT(*) FROM sys_role)            as total_roles,
       (SELECT COUNT(*) FROM sys_dept)            as total_depts,
       (SELECT COUNT(*) FROM SYS_MENU_ENTITY)            as total_menus,
       (SELECT COUNT(*) FROM sys_permission)      as total_permissions,
       (SELECT COUNT(*) FROM sys_user_role)       as total_user_roles,
       (SELECT COUNT(*) FROM sys_role_permission) as total_role_permissions;

-- 显示测试账户信息
SELECT '=== 测试账户信息 ==='                                     as info,
       'username: superadmin, password: 123456, role: 超级管理员' as account1,
       'username: admin, password: 123456, role: 系统管理员'      as account2,
       'username: deptadmin, password: 123456, role: 部门管理员'  as account3,
       'username: testuser, password: 123456, role: 普通用户'     as account4;

-- ================================
-- 8. 清理临时函数
-- ================================

-- 删除临时创建的雪花算法ID生成函数
DROP FUNCTION IF EXISTS generate_snowflake_id;
