package com.ai.cutover.module.system.model.entity;

import com.ai.cutover.common.entity.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统角色实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("sys_role")
public class SysRoleEntity extends BaseEntity {

	/**
	 * 角色编码
	 */
	@Column("role_code")
	private String roleCode;

	/**
	 * 角色名称
	 */
	@Column("role_name")
	private String roleName;

	/**
	 * 角色描述
	 */
	@Column("description")
	private String description;

	/**
	 * 显示顺序
	 */
	@Column("sort_order")
	private Integer sortOrder;

	/**
	 * 角色状态（NORMAL-正常，DISABLED-禁用）
	 */
	@Column("status")
	private String status;

	/**
	 * 数据权限范围（ALL-全部数据，DEPT_AND_CHILD-本部门及子部门，DEPT_ONLY-仅本部门，SELF_ONLY-仅本人，CUSTOM-自定义）
	 */
	@Column("data_scope")
	private String dataScope;

	/**
	 * 是否为内置角色（Y-是，N-否）
	 */
	@Column("builtin_flag")
	private String builtinFlag;

	/**
	 * 备注
	 */
	@Column("remark")
	private String remark;

}
