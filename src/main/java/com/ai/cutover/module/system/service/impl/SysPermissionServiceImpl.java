package com.ai.cutover.module.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ai.cutover.common.constant.StatusConstants;
import com.ai.cutover.common.util.Assert;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.module.system.dao.SysPermissionDao;
import com.ai.cutover.module.system.model.entity.SysPermissionEntity;
import com.ai.cutover.module.system.model.entity.SysRolePermissionEntity;
import com.ai.cutover.module.system.model.entity.SysUserRoleEntity;
import com.ai.cutover.module.system.model.dto.SysPermissionDTO;
import com.ai.cutover.module.system.service.SysPermissionService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.query.QueryWrapper;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统权限服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysPermissionServiceImpl implements SysPermissionService {

	private final SysPermissionDao sysPermissionDao;

	private final Converter converter;

	@Override
	public SysPermissionDTO getPermissionById(Long permissionId) {
		Assert.notNull(permissionId, ErrorMessages.Common.PARAM_MISSING);
		return converter.convert(sysPermissionDao.selectOneById(permissionId), SysPermissionDTO.class);
	}

	@Override
	public SysPermissionDTO getPermissionByCode(String permissionCode) {
		Assert.hasText(permissionCode, ErrorMessages.Common.PARAM_MISSING);

		return QueryChain.of(sysPermissionDao)
			.where(SysPermissionEntity::getPermissionCode)
			.eq(permissionCode)
			.and(SysPermissionEntity::getStatus)
			.eq(StatusConstants.Permission.NORMAL)
			.oneAs(SysPermissionDTO.class);
	}

	@Override
	public Page<SysPermissionDTO> getPermissionPage(Integer pageNum, Integer pageSize, String permissionCode,
			String permissionName, String permissionType, String status) {
		// 构建查询条件
		QueryWrapper wrapper = QueryWrapper.create()
			.where(SysPermissionEntity::getPermissionCode)
			.like(permissionCode, StrUtil.isNotBlank(permissionCode))
			.and(SysPermissionEntity::getPermissionName)
			.like(permissionName, StrUtil.isNotBlank(permissionName))
			.and(SysPermissionEntity::getPermissionType)
			.eq(permissionType, StrUtil.isNotBlank(permissionType))
			.and(SysPermissionEntity::getStatus)
			.eq(status, StrUtil.isNotBlank(status))
			.orderBy(SysPermissionEntity::getSortOrder, true);

		// 直接分页查询并转换为DTO
		return sysPermissionDao.paginateAs(pageNum, pageSize, wrapper, SysPermissionDTO.class);
	}

	@Override
	public List<SysPermissionDTO> getPermissionsByRoleId(Long roleId) {
		Assert.notNull(roleId, ErrorMessages.Common.PARAM_MISSING);

		return QueryChain.of(sysPermissionDao)
			.leftJoin(SysRolePermissionEntity.class)
			.on(SysPermissionEntity::getId, SysRolePermissionEntity::getPermissionId)
			.where(SysRolePermissionEntity::getRoleId)
			.eq(roleId)
			.and(SysPermissionEntity::getStatus)
			.eq(StatusConstants.Permission.NORMAL)
			.orderBy(SysPermissionEntity::getSortOrder, true)
			.listAs(SysPermissionDTO.class);
	}

	@Override
	public List<SysPermissionDTO> getPermissionsByUserId(Long userId) {
		Assert.notNull(userId, ErrorMessages.Common.PARAM_MISSING);

		return QueryChain.of(sysPermissionDao)
			.leftJoin(SysRolePermissionEntity.class)
			.on(SysPermissionEntity::getId, SysRolePermissionEntity::getPermissionId)
			.leftJoin(SysUserRoleEntity.class)
			.on(SysRolePermissionEntity::getRoleId, SysUserRoleEntity::getRoleId)
			.where(SysUserRoleEntity::getUserId)
			.eq(userId)
			.and(SysPermissionEntity::getStatus)
			.eq(StatusConstants.Permission.NORMAL)
			.orderBy(SysPermissionEntity::getSortOrder, true)
			.listAs(SysPermissionDTO.class);
	}

	@Override
	public List<SysPermissionDTO> getAllPermissions() {
		return QueryChain.of(sysPermissionDao)
			.where(SysPermissionEntity::getStatus)
			.eq(StatusConstants.Permission.NORMAL)
			.orderBy(SysPermissionEntity::getSortOrder, true)
			.listAs(SysPermissionDTO.class);
	}

	@Override
	public List<SysPermissionDTO> getPermissionsByType(String permissionType) {
		Assert.hasText(permissionType, ErrorMessages.Common.PARAM_MISSING);

		return QueryChain.of(sysPermissionDao)
			.where(SysPermissionEntity::getPermissionType)
			.eq(permissionType)
			.and(SysPermissionEntity::getStatus)
			.eq(StatusConstants.Permission.NORMAL)
			.orderBy(SysPermissionEntity::getSortOrder, true)
			.listAs(SysPermissionDTO.class);
	}

	@Override
	public boolean existsByPermissionCode(String permissionCode, Long excludeId) {
		Assert.hasText(permissionCode, ErrorMessages.Common.PARAM_MISSING);

		return QueryChain.of(sysPermissionDao)
			.where(SysPermissionEntity::getPermissionCode)
			.eq(permissionCode)
			.and(SysPermissionEntity::getId)
			.ne(excludeId, excludeId != null)
			.exists();
	}

	@Override
	public boolean existsByPermissionName(String permissionName, Long excludeId) {
		Assert.hasText(permissionName, ErrorMessages.Common.PARAM_MISSING);

		return QueryChain.of(sysPermissionDao)
			.where(SysPermissionEntity::getPermissionName)
			.eq(permissionName)
			.and(SysPermissionEntity::getId)
			.ne(excludeId, excludeId != null)
			.exists();
	}

}
