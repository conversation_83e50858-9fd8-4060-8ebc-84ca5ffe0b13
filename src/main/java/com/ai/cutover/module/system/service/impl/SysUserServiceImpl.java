package com.ai.cutover.module.system.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.ai.cutover.common.constant.BusinessConstants;
import com.ai.cutover.common.constant.StatusConstants;
import com.ai.cutover.common.enums.ResourceType;
import com.ai.cutover.common.util.Assert;
import com.ai.cutover.common.util.CacheUtil;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.module.system.dao.*;
import com.ai.cutover.module.system.model.entity.*;
import com.ai.cutover.module.system.model.dto.SysUserDTO;

import static com.ai.cutover.module.system.model.entity.table.SysPermissionEntityTableDef.SYS_PERMISSION_ENTITY;
import static com.ai.cutover.module.system.model.entity.table.SysRoleEntityTableDef.SYS_ROLE_ENTITY;
import static com.ai.cutover.module.system.model.entity.table.SysUserRoleEntityTableDef.SYS_USER_ROLE_ENTITY;

import com.ai.cutover.module.system.model.req.CreateUserReq;
import com.ai.cutover.module.system.model.req.UpdateUserReq;
import com.ai.cutover.module.system.model.req.UserQueryReq;
import com.ai.cutover.module.system.service.SysUserService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统用户服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl implements SysUserService {

	private final SysUserDao sysUserDao;

	private final SysDeptDao sysDeptDao;

	private final SysUserRoleDao sysUserRoleDao;

	private final Converter converter;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public SysUserDTO createUser(CreateUserReq request) {
		// 1. 参数校验
		validateCreateUserRequest(request);

		// 2. 检查用户名、邮箱、手机号是否已存在
		checkUserUniqueness(request.getUsername(), request.getEmail(), request.getPhone(), null);

		// 3. 构建用户实体
		SysUserEntity user = buildUserFromCreateRequest(request);

		// 4. 保存用户
		sysUserDao.insert(user);

		// 5. 分配角色
		if (CollUtil.isNotEmpty(request.getRoleIds())) {
			assignUserRoles(user.getId(), request.getRoleIds());
		}

		// 6. 返回用户DTO
		return getUserById(user.getId());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public SysUserDTO updateUser(UpdateUserReq request) {
		// 检查用户是否存在
		SysUserEntity existingUser = sysUserDao.selectOneById(request.getId());
		Assert.resourceExists(existingUser, ResourceType.USER);

		// 检查是否为内置用户
		Assert.isFalse(BusinessConstants.YesNo.YES.equals(existingUser.getBuiltinFlag()),
				ErrorMessages.Common.CANNOT_MODIFY_BUILTIN, "用户[" + existingUser.getUsername() + "]");

		// 检查邮箱、手机号唯一性
		checkUserUniqueness(null, request.getEmail(), request.getPhone(), request.getId());

		// 更新用户信息
		SysUserEntity updateUser = converter.convert(request, SysUserEntity.class);
		sysUserDao.update(updateUser);

		// 更新用户角色
		if (request.getRoleIds() != null) {
			assignUserRoles(request.getId(), request.getRoleIds());
		}

		return getUserById(request.getId());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteUser(Long userId) {
		SysUserEntity user = sysUserDao.selectOneById(userId);
		Assert.resourceExists(user, ResourceType.USER);
		Assert.isFalse(BusinessConstants.YesNo.YES.equals(user.getBuiltinFlag()),
				ErrorMessages.Common.CANNOT_DELETE_BUILTIN, "用户[" + user.getUsername() + "]");

		// 删除用户角色关联
		QueryWrapper deleteWrapper = QueryWrapper.create().where(SysUserRoleEntity::getUserId).eq(userId);
		sysUserRoleDao.deleteByQuery(deleteWrapper);

		// 删除用户
		sysUserDao.deleteById(userId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchDeleteUsers(List<Long> userIds) {
		if (CollUtil.isEmpty(userIds)) {
			return;
		}

		// 检查是否包含内置用户
		List<SysUserEntity> users = sysUserDao.selectListByIds(userIds);
		boolean hasBuiltinUser = users.stream()
			.anyMatch(user -> BusinessConstants.YesNo.YES.equals(user.getBuiltinFlag()));
		Assert.isFalse(hasBuiltinUser, ErrorMessages.Common.CANNOT_DELETE_BUILTIN, "内置用户");

		// 删除用户角色关联
		QueryWrapper deleteWrapper = QueryWrapper.create().where(SysUserRoleEntity::getUserId).in(userIds);
		sysUserRoleDao.deleteByQuery(deleteWrapper);

		// 批量删除用户
		sysUserDao.deleteBatchByIds(userIds);
	}

	@Override
	public SysUserDTO getUserById(Long userId) {
		SysUserEntity user = sysUserDao.selectOneById(userId);
		if (user == null) {
			return null;
		}

		SysUserDTO userDTO = converter.convert(user, SysUserDTO.class);

		// 设置部门名称
		if (user.getDeptId() != null) {
			SysDeptEntity dept = sysDeptDao.selectOneById(user.getDeptId());
			if (dept != null) {
				userDTO.setDeptName(dept.getDeptName());
			}
		}

		// 设置角色信息
		List<SysRoleEntity> roles = QueryChain.of(SysRoleEntity.class)
			.leftJoin(SysUserRoleEntity.class)
			.on(SysRoleEntity::getId, SysUserRoleEntity::getRoleId)
			.where(SysUserRoleEntity::getUserId)
			.eq(userId)
			.and(SysRoleEntity::getStatus)
			.eq(StatusConstants.Role.NORMAL)
			.list();
		if (CollUtil.isNotEmpty(roles)) {
			userDTO.setRoleIds(roles.stream().map(SysRoleEntity::getId).collect(Collectors.toList()));
			userDTO.setRoleNames(roles.stream().map(SysRoleEntity::getRoleName).collect(Collectors.toList()));
		}

		return userDTO;
	}

	@Override
	public SysUserEntity getUserEntityById(Long userId) {
		return sysUserDao.selectOneById(userId);
	}

	@Override
	public SysUserDTO getUserByUsername(String username) {
		SysUserEntity user = QueryChain.of(SysUserEntity.class).where(SysUserEntity::getUsername).eq(username).one();
		return user != null ? getUserById(user.getId()) : null;
	}

	@Override
	public Page<SysUserDTO> getUserPage(UserQueryReq request) {
		Page<SysUserEntity> userPage = QueryChain.of(SysUserEntity.class)
			.where(SysUserEntity::getUsername)
			.like(request.getUsername(), StrUtil.isNotBlank(request.getUsername()))
			.and(SysUserEntity::getRealName)
			.like(request.getRealName(), StrUtil.isNotBlank(request.getRealName()))
			.and(SysUserEntity::getPhone)
			.like(request.getPhone(), StrUtil.isNotBlank(request.getPhone()))
			.and(SysUserEntity::getEmail)
			.like(request.getEmail(), StrUtil.isNotBlank(request.getEmail()))
			.and(SysUserEntity::getStatus)
			.eq(request.getStatus(), StrUtil.isNotBlank(request.getStatus()))
			.and(SysUserEntity::getDeptId)
			.eq(request.getDeptId(), request.getDeptId() != null)
			.orderBy(SysUserEntity::getCreateTime, false)
			.page(new Page<>(request.getPageNum(), request.getPageSize()));

		// 使用 CacheUtil 批量查询部门信息，避免N+1查询问题
		List<Long> deptIds = userPage.getRecords()
			.stream()
			.map(SysUserEntity::getDeptId)
			.filter(Objects::nonNull)
			.collect(Collectors.toList());

		Map<Long, String> deptNameMap = CacheUtil.batchQueryToMap(deptIds, sysDeptDao::selectListByIds,
				SysDeptEntity::getId, SysDeptEntity::getDeptName);

		return userPage.map(user -> {
			SysUserDTO dto = converter.convert(user, SysUserDTO.class);
			// 设置部门名称
			if (user.getDeptId() != null) {
				dto.setDeptName(deptNameMap.get(user.getDeptId()));
			}
			return dto;
		});
	}

	@Override
	public List<SysUserDTO> getAllUsers() {
		List<SysUserEntity> userList = QueryChain.of(SysUserEntity.class)
			.where(SysUserEntity::getStatus)
			.eq(StatusConstants.User.NORMAL)
			.orderBy(SysUserEntity::getCreateTime, false)
			.list();

		List<SysUserDTO> userDTOList = converter.convert(userList, SysUserDTO.class);

		// 填充部门名称
		fillUserDeptNames(userDTOList);

		return userDTOList;
	}

	@Override
	public List<SysUserDTO> getUsersByDeptId(Long deptId) {
		List<SysUserEntity> userList = QueryChain.of(SysUserEntity.class)
			.where(SysUserEntity::getDeptId)
			.eq(deptId)
			.and(SysUserEntity::getStatus)
			.eq(StatusConstants.User.NORMAL)
			.list();

		List<SysUserDTO> userDTOList = converter.convert(userList, SysUserDTO.class);

		// 填充部门名称
		fillUserDeptNames(userDTOList);

		return userDTOList;
	}

	@Override
	public List<SysUserDTO> getUsersByRoleId(Long roleId) {
		List<Long> userIds = QueryChain.of(SysUserRoleEntity.class)
			.select(SYS_USER_ROLE_ENTITY.USER_ID)
			.where(SysUserRoleEntity::getRoleId)
			.eq(roleId)
			.listAs(Long.class);
		if (CollUtil.isEmpty(userIds)) {
			return CollUtil.newArrayList();
		}

		List<SysUserEntity> users = sysUserDao.selectListByIds(userIds);
		return converter.convert(users, SysUserDTO.class);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void resetPassword(Long userId, String newPassword) {
		SysUserEntity user = sysUserDao.selectOneById(userId);
		Assert.resourceExists(user, ResourceType.USER);

		// 校验新密码强度
		validatePasswordStrength(newPassword);

		String encodedPassword = encodePassword(newPassword);
		UpdateChain.of(SysUserEntity.class)
			.set(SysUserEntity::getPassword, encodedPassword)
			.set(SysUserEntity::getPasswordExpireTime, LocalDateTime.now().plusMonths(3))
			.where(SysUserEntity::getId)
			.eq(userId)
			.update();

		// 强制用户重新登录（踢出所有会话）
		StpUtil.kickout(userId);
		log.info("管理员重置用户密码成功，已强制退出用户所有会话: userId={}", userId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void changePassword(Long userId, String oldPassword, String newPassword) {
		SysUserEntity user = sysUserDao.selectOneById(userId);
		Assert.resourceExists(user, ResourceType.USER);

		// 1. 校验原密码
		Assert.isTrue(verifyPassword(oldPassword, user.getPassword()), ErrorMessages.User.OLD_PASSWORD_ERROR);

		// 2. 校验新密码不能与原密码相同
		Assert.isFalse(verifyPassword(newPassword, user.getPassword()), ErrorMessages.User.PASSWORD_SAME_AS_OLD);

		// 3. 校验新密码强度
		validatePasswordStrength(newPassword);

		// 4. 更新密码
		String encodedPassword = encodePassword(newPassword);
		UpdateChain.of(SysUserEntity.class)
			.set(SysUserEntity::getPassword, encodedPassword)
			.set(SysUserEntity::getPasswordExpireTime, LocalDateTime.now().plusMonths(3))
			.where(SysUserEntity::getId)
			.eq(userId)
			.update();

		// 5. 强制用户重新登录（踢出所有会话）
		StpUtil.kickout(userId);
		log.info("用户修改密码成功，已强制退出所有会话: userId={}", userId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateUserStatus(Long userId, String status) {
		UpdateChain.of(SysUserEntity.class)
			.set(SysUserEntity::getStatus, status)
			.where(SysUserEntity::getId)
			.eq(userId)
			.update();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void assignUserRoles(Long userId, List<Long> roleIds) {
		// 删除原有角色关联
		QueryWrapper deleteWrapper = QueryWrapper.create().where(SysUserRoleEntity::getUserId).eq(userId);
		sysUserRoleDao.deleteByQuery(deleteWrapper);

		// 批量添加新的角色关联
		if (CollUtil.isNotEmpty(roleIds)) {
			List<SysUserRoleEntity> userRoles = roleIds.stream()
				.map(roleId -> new SysUserRoleEntity(userId, roleId))
				.collect(Collectors.toList());
			sysUserRoleDao.insertBatch(userRoles);
		}
	}

	@Override
	public Set<String> getUserPermissions(Long userId) {
		List<String> permissions = QueryChain.of(SysPermissionEntity.class)
			.select(SYS_PERMISSION_ENTITY.PERMISSION_CODE)
			.leftJoin(SysRolePermissionEntity.class)
			.on(SysPermissionEntity::getId, SysRolePermissionEntity::getPermissionId)
			.leftJoin(SysUserRoleEntity.class)
			.on(SysRolePermissionEntity::getRoleId, SysUserRoleEntity::getRoleId)
			.where(SysUserRoleEntity::getUserId)
			.eq(userId)
			.and(SysPermissionEntity::getStatus)
			.eq(StatusConstants.Permission.NORMAL)
			.listAs(String.class);
		return new HashSet<>(permissions);
	}

	@Override
	public Set<String> getUserRoles(Long userId) {
		List<String> roles = QueryChain.of(SysRoleEntity.class)
			.select(SYS_ROLE_ENTITY.ROLE_CODE)
			.leftJoin(SysUserRoleEntity.class)
			.on(SysRoleEntity::getId, SysUserRoleEntity::getRoleId)
			.where(SysUserRoleEntity::getUserId)
			.eq(userId)
			.and(SysRoleEntity::getStatus)
			.eq(StatusConstants.Role.NORMAL)
			.listAs(String.class);
		return new HashSet<>(roles);
	}

	@Override
	public boolean existsByUsername(String username, Long excludeId) {
		return QueryChain.of(SysUserEntity.class)
			.where(SysUserEntity::getUsername)
			.eq(username)
			.and(SysUserEntity::getId)
			.ne(excludeId, excludeId != null)
			.exists();
	}

	@Override
	public boolean existsByEmail(String email, Long excludeId) {
		if (StrUtil.isBlank(email)) {
			return false;
		}
		return QueryChain.of(SysUserEntity.class)
			.where(SysUserEntity::getEmail)
			.eq(email)
			.and(SysUserEntity::getId)
			.ne(excludeId, excludeId != null)
			.exists();
	}

	@Override
	public boolean existsByPhone(String phone, Long excludeId) {
		if (StrUtil.isBlank(phone)) {
			return false;
		}
		return QueryChain.of(SysUserEntity.class)
			.where(SysUserEntity::getPhone)
			.eq(phone)
			.and(SysUserEntity::getId)
			.ne(excludeId, excludeId != null)
			.exists();
	}

	// 私有方法

	private void validateCreateUserRequest(CreateUserReq request) {
		Assert.hasText(request.getUsername(), ErrorMessages.User.USERNAME_REQUIRED);
		Assert.hasText(request.getPassword(), ErrorMessages.User.PASSWORD_REQUIRED);
		Assert.hasText(request.getRealName(), ErrorMessages.User.REAL_NAME_REQUIRED);
		Assert.isTrue(request.getPassword().equals(request.getConfirmPassword()),
				ErrorMessages.User.PASSWORD_CONFIRM_NOT_MATCH);

		// 校验密码强度
		validatePasswordStrength(request.getPassword());
	}

	private void checkUserUniqueness(String username, String email, String phone, Long excludeId) {
		if (StrUtil.isNotBlank(username)) {
			Assert.isFalse(existsByUsername(username, excludeId), ErrorMessages.User.USERNAME_ALREADY_EXISTS, username);
		}
		if (StrUtil.isNotBlank(email)) {
			Assert.isFalse(existsByEmail(email, excludeId), ErrorMessages.User.EMAIL_ALREADY_EXISTS, email);
		}
		if (StrUtil.isNotBlank(phone)) {
			Assert.isFalse(existsByPhone(phone, excludeId), ErrorMessages.User.PHONE_ALREADY_EXISTS, phone);
		}
	}

	private SysUserEntity buildUserFromCreateRequest(CreateUserReq request) {
		SysUserEntity user = converter.convert(request, SysUserEntity.class);
		user.setPassword(encodePassword(request.getPassword()));
		user.setStatus(StrUtil.isNotBlank(request.getStatus()) ? request.getStatus() : StatusConstants.User.NORMAL);
		user.setBuiltinFlag(BusinessConstants.YesNo.NO);
		user.setPasswordExpireTime(LocalDateTime.now().plusMonths(3));
		return user;
	}

	@Override
	public void updateLastLoginInfo(Long userId, String loginIp) {
		UpdateChain.of(SysUserEntity.class)
			.set(SysUserEntity::getLastLoginTime, LocalDateTime.now())
			.set(SysUserEntity::getLastLoginIp, loginIp)
			.where(SysUserEntity::getId)
			.eq(userId)
			.update();
	}

	@Override
	public void lockUser(Long userId) {
		UpdateChain.of(SysUserEntity.class)
			.set(SysUserEntity::getStatus, StatusConstants.User.LOCKED)
			.set(SysUserEntity::getLockTime, LocalDateTime.now())
			.where(SysUserEntity::getId)
			.eq(userId)
			.update();
	}

	@Override
	public void unlockUser(Long userId) {
		UpdateChain.of(SysUserEntity.class)
			.set(SysUserEntity::getStatus, StatusConstants.User.NORMAL)
			.set(SysUserEntity::getLockTime, null)
			.where(SysUserEntity::getId)
			.eq(userId)
			.update();
	}

	@Override
	public boolean verifyPassword(String rawPassword, String encodedPassword) {
		return BCrypt.checkpw(rawPassword, encodedPassword);
	}

	@Override
	public String encodePassword(String rawPassword) {
		return BCrypt.hashpw(rawPassword, BCrypt.gensalt());
	}

	/**
	 * 校验密码强度
	 */
	private void validatePasswordStrength(String password) {
		if (StrUtil.isBlank(password)) {
			Assert.fail(ErrorMessages.User.PASSWORD_REQUIRED);
		}

		// 长度校验
		Assert.isTrue(password.length() >= 6, ErrorMessages.User.PASSWORD_TOO_SHORT, 6);
		Assert.isTrue(password.length() <= 20, ErrorMessages.User.PASSWORD_TOO_LONG, 20);

		// 格式校验：必须包含字母和数字
		Assert.matches(password, BusinessConstants.Regex.PASSWORD, ErrorMessages.User.PASSWORD_INVALID_FORMAT);
	}

	/**
	 * 填充用户部门名称
	 */
	private void fillUserDeptNames(List<SysUserDTO> userDTOList) {
		if (CollUtil.isEmpty(userDTOList)) {
			return;
		}

		// 批量查询部门信息
		List<Long> deptIds = userDTOList.stream()
			.map(SysUserDTO::getDeptId)
			.filter(Objects::nonNull)
			.distinct()
			.collect(Collectors.toList());

		Map<Long, String> deptNameMap = Map.of();
		if (CollUtil.isNotEmpty(deptIds)) {
			List<SysDeptEntity> depts = sysDeptDao.selectListByIds(deptIds);
			deptNameMap = depts.stream().collect(Collectors.toMap(SysDeptEntity::getId, SysDeptEntity::getDeptName));
		}

		// 填充部门名称
		final Map<Long, String> finalDeptNameMap = deptNameMap;
		userDTOList.forEach(user -> {
			if (user.getDeptId() != null) {
				user.setDeptName(finalDeptNameMap.get(user.getDeptId()));
			}
		});
	}

}
