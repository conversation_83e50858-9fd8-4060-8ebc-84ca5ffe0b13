package com.ai.cutover.module.system.model.req;

import com.ai.cutover.module.system.model.entity.SysMenuEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 创建菜单请求类
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysMenuEntity.class)
public class CreateMenuReq {

	/**
	 * 父菜单ID（0表示根菜单）
	 */
	@NotNull(message = "父菜单ID不能为空")
	private Long parentId;

	/**
	 * 菜单名称
	 */
	@NotBlank(message = "菜单名称不能为空")
	@Size(max = 100, message = "菜单名称长度不能超过100个字符")
	private String menuName;

	/**
	 * 菜单标题
	 */
	@Size(max = 100, message = "菜单标题长度不能超过100个字符")
	private String title;

	/**
	 * 菜单类型（DIRECTORY-目录，MENU-菜单，BUTTON-按钮）
	 */
	@NotBlank(message = "菜单类型不能为空")
	private String menuType;

	/**
	 * 路由地址
	 */
	@Size(max = 200, message = "路由地址长度不能超过200个字符")
	private String path;

	/**
	 * 组件路径
	 */
	@Size(max = 200, message = "组件路径长度不能超过200个字符")
	private String component;

	/**
	 * 权限标识
	 */
	@Size(max = 100, message = "权限标识长度不能超过100个字符")
	private String permission;

	/**
	 * 菜单图标
	 */
	@Size(max = 100, message = "菜单图标长度不能超过100个字符")
	private String icon;

	/**
	 * 显示顺序
	 */
	private Integer sortOrder;

	/**
	 * 是否外链（Y-是，N-否）
	 */
	private String isFrame;

	/**
	 * 是否缓存（Y-是，N-否）
	 */
	private String isCache;

	/**
	 * 是否显示（Y-是，N-否）
	 */
	private String visible;

	/**
	 * 备注
	 */
	@Size(max = 500, message = "备注长度不能超过500个字符")
	private String remarks;

}
