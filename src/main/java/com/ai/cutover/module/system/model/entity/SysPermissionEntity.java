package com.ai.cutover.module.system.model.entity;

import com.ai.cutover.common.entity.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统权限实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("sys_permission")
public class SysPermissionEntity extends BaseEntity {

	/**
	 * 权限编码
	 */
	@Column("permission_code")
	private String permissionCode;

	/**
	 * 权限名称
	 */
	@Column("permission_name")
	private String permissionName;

	/**
	 * 权限类型（MENU-菜单，BUTTON-按钮，API-接口，DATA-数据）
	 */
	@Column("permission_type")
	private String permissionType;

	/**
	 * 资源路径
	 */
	@Column("resource_path")
	private String resourcePath;

	/**
	 * 请求方法（GET,POST,PUT,DELETE等）
	 */
	@Column("request_method")
	private String requestMethod;

	/**
	 * 权限描述
	 */
	@Column("description")
	private String description;

	/**
	 * 显示顺序
	 */
	@Column("sort_order")
	private Integer sortOrder;

	/**
	 * 权限状态（NORMAL-正常，DISABLED-禁用）
	 */
	@Column("status")
	private String status;

	/**
	 * 备注
	 */
	@Column("remark")
	private String remark;

}
