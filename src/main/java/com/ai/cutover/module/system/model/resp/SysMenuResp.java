package com.ai.cutover.module.system.model.resp;

import com.ai.cutover.module.system.model.entity.SysMenuEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统菜单响应类
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysMenuEntity.class, reverseConvertGenerate = false)
public class SysMenuResp {

	/**
	 * 菜单ID
	 */
	private Long id;

	/**
	 * 父菜单ID（0表示根菜单）
	 */
	private Long parentId;

	/**
	 * 菜单名称
	 */
	private String menuName;

	/**
	 * 菜单标题
	 */
	private String title;

	/**
	 * 菜单类型（DIRECTORY-目录，MENU-菜单，BUTTON-按钮）
	 */
	private String menuType;

	/**
	 * 路由地址
	 */
	private String path;

	/**
	 * 组件路径
	 */
	private String component;

	/**
	 * 权限标识
	 */
	private String permission;

	/**
	 * 菜单图标
	 */
	private String icon;

	/**
	 * 显示顺序
	 */
	private Integer sortOrder;

	/**
	 * 是否外链（Y-是，N-否）
	 */
	private String isFrame;

	/**
	 * 是否缓存（Y-是，N-否）
	 */
	private String isCache;

	/**
	 * 是否显示（Y-是，N-否）
	 */
	private String visible;

	/**
	 * 菜单状态（NORMAL-正常，DISABLED-禁用）
	 */
	private String status;

	/**
	 * 备注
	 */
	private String remarks;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人
	 */
	private Long createBy;

	/**
	 * 更新人
	 */
	private Long updateBy;

	// 扩展字段

	/**
	 * 父菜单名称
	 */
	private String parentName;

	/**
	 * 子菜单列表
	 */
	private List<SysMenuResp> children;

	/**
	 * 是否有子菜单
	 */
	private Boolean hasChildren;

}
