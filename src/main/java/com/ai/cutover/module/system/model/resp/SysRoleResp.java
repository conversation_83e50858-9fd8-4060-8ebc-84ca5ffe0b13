package com.ai.cutover.module.system.model.resp;

import io.github.linpeilie.annotations.AutoMapper;
import com.ai.cutover.module.system.model.entity.SysRoleEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统角色响应类
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysRoleEntity.class, reverseConvertGenerate = false)
public class SysRoleResp {

	/**
	 * 角色ID
	 */
	private Long id;

	/**
	 * 角色编码
	 */
	private String roleCode;

	/**
	 * 角色名称
	 */
	private String roleName;

	/**
	 * 角色描述
	 */
	private String description;

	/**
	 * 显示顺序
	 */
	private Integer sortOrder;

	/**
	 * 数据权限范围（ALL-全部，DEPT_AND_CHILD-本部门及子部门，DEPT_ONLY-仅本部门，SELF_ONLY-仅本人，CUSTOM-自定义）
	 */
	private String dataScope;

	/**
	 * 角色状态（NORMAL-正常，DISABLED-禁用）
	 */
	private String status;

	/**
	 * 是否内置角色（Y-是，N-否）
	 */
	private String builtinFlag;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人
	 */
	private Long createBy;

	/**
	 * 更新人
	 */
	private Long updateBy;

	// 扩展字段

	/**
	 * 权限ID列表
	 */
	private List<Long> permissionIds;

	/**
	 * 权限名称列表
	 */
	private List<String> permissionNames;

	/**
	 * 用户数量
	 */
	private Integer userCount;

}
