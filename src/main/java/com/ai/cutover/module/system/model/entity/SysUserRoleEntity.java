package com.ai.cutover.module.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

/**
 * 用户角色关联实体类
 *
 * <AUTHOR>
 */
@Data
@Table("sys_user_role")
public class SysUserRoleEntity {

	/**
	 * 主键ID
	 */
	@Id(keyType = KeyType.Auto)
	private Long id;

	/**
	 * 用户ID
	 */
	@Column("user_id")
	private Long userId;

	/**
	 * 角色ID
	 */
	@Column("role_id")
	private Long roleId;

	public SysUserRoleEntity() {
	}

	public SysUserRoleEntity(Long userId, Long roleId) {
		this.userId = userId;
		this.roleId = roleId;
	}

}
