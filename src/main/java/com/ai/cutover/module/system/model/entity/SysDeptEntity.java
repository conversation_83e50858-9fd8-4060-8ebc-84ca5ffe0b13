package com.ai.cutover.module.system.model.entity;

import com.ai.cutover.common.entity.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统部门实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("sys_dept")
public class SysDeptEntity extends BaseEntity {

	/**
	 * 父部门ID（0表示根部门）
	 */
	@Column("parent_id")
	private Long parentId;

	/**
	 * 部门编码
	 */
	@Column("dept_code")
	private String deptCode;

	/**
	 * 部门名称
	 */
	@Column("dept_name")
	private String deptName;

	/**
	 * 部门类型（COMPANY-公司，DEPT-部门，GROUP-小组）
	 */
	@Column("dept_type")
	private String deptType;

	/**
	 * 显示顺序
	 */
	@Column("sort_order")
	private Integer sortOrder;

	/**
	 * 负责人ID
	 */
	@Column("leader_id")
	private Long leaderId;

	/**
	 * 联系电话
	 */
	@Column("phone")
	private String phone;

	/**
	 * 邮箱
	 */
	@Column("email")
	private String email;

	/**
	 * 部门地址
	 */
	@Column("address")
	private String address;

	/**
	 * 部门状态（NORMAL-正常，DISABLED-禁用）
	 */
	@Column("status")
	private String status;

	/**
	 * 祖级列表（用逗号分隔的父级ID列表）
	 */
	@Column("ancestors")
	private String ancestors;

	/**
	 * 层级深度
	 */
	@Column("level")
	private Integer level;

	/**
	 * 备注
	 */
	@Column("remark")
	private String remark;

}
