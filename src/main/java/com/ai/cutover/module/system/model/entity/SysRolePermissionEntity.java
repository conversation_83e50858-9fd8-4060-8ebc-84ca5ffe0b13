package com.ai.cutover.module.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

/**
 * 角色权限关联实体类
 *
 * <AUTHOR>
 */
@Data
@Table("sys_role_permission")
public class SysRolePermissionEntity {

	/**
	 * 主键ID
	 */
	@Id(keyType = KeyType.Auto)
	private Long id;

	/**
	 * 角色ID
	 */
	@Column("role_id")
	private Long roleId;

	/**
	 * 权限ID
	 */
	@Column("permission_id")
	private Long permissionId;

	public SysRolePermissionEntity() {
	}

	public SysRolePermissionEntity(Long roleId, Long permissionId) {
		this.roleId = roleId;
		this.permissionId = permissionId;
	}

}
