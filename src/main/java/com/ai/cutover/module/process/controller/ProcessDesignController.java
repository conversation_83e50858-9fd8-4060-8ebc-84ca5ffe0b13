package com.ai.cutover.module.process.controller;

import com.ai.cutover.module.process.model.dto.ProcessDefinitionDTO;
import com.ai.cutover.module.process.model.entity.ProcessDefinitionEntity;
import com.ai.cutover.module.process.model.req.*;
import com.ai.cutover.module.process.model.resp.ProcessDeploymentResp;
import com.ai.cutover.module.process.service.ProcessDesignService;
import com.mybatisflex.core.paginate.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 流程设计控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/process-design")
@RequiredArgsConstructor
@Validated
public class ProcessDesignController {

	private final ProcessDesignService processDesignService;

	/**
	 * 创建流程定义
	 */
	@PostMapping("/create")
	public ProcessDefinitionDTO createProcessDefinition(@Valid @RequestBody CreateProcessReq request) {
		return processDesignService.createProcessDefinition(request);
	}

	/**
	 * 更新流程定义
	 */
	@PostMapping("/update")
	public ProcessDefinitionDTO updateProcessDefinition(@Valid @RequestBody ProcessDefinitionEntity processInfo) {
		return processDesignService.updateProcessDefinition(processInfo);
	}

	/**
	 * 保存流程设计
	 */
	@PostMapping("/save-design")
	public ProcessDefinitionDTO saveProcessDesign(@Valid @RequestBody SaveDesignReq request) {
		return processDesignService.saveProcessDesign(request.getProcessKey(), request.getBpmnXml(),
				request.getDiagramSvg());
	}

	/**
	 * 部署流程定义（部署最新版本）
	 */
	@PostMapping("/deploy")
	public ProcessDeploymentResp deployProcess(@Valid @RequestBody DeployProcessReq request) {
		return processDesignService.deployProcess(request.getProcessKey());
	}

	/**
	 * 重新部署指定版本的流程定义
	 */
	@PostMapping("/redeploy-version")
	public ProcessDeploymentResp redeployProcessVersion(@Valid @RequestBody RedeployVersionReq request) {
		return processDesignService.redeployProcessVersion(request.getProcessKey(), request.getVersion());
	}

	/**
	 * 取消部署流程定义
	 */
	@PostMapping("/undeploy")
	public void undeployProcess(@Valid @RequestBody UndeployProcessReq request) {
		processDesignService.undeployProcess(request.getProcessKey(),
				request.getCascade() != null ? request.getCascade() : false);
	}

	/**
	 * 激活流程定义
	 */
	@PostMapping("/activate")
	public void activateProcess(@Valid @RequestBody ActivateProcessDesignReq request) {
		processDesignService.activateProcess(request.getProcessKey());
	}

	/**
	 * 挂起流程定义
	 */
	@PostMapping("/suspend")
	public void suspendProcess(@Valid @RequestBody SuspendProcessDesignReq request) {
		processDesignService.suspendProcess(request.getProcessKey());
	}

	/**
	 * 查询流程定义详情
	 */
	@GetMapping("/detail")
	public ProcessDefinitionDTO getProcessDefinitionDetail(@RequestParam @NotNull Long id) {
		return processDesignService.getProcessDefinitionById(id);
	}

	/**
	 * 根据流程Key查询流程定义
	 */
	@GetMapping("/by-key")
	public ProcessDefinitionDTO getProcessDefinitionByKey(@RequestParam @NotBlank String processKey) {
		return processDesignService.getProcessDefinitionByKey(processKey);
	}

	/**
	 * 分页查询流程定义列表
	 */
	@GetMapping("/list")
	public Page<ProcessDefinitionDTO> getProcessDefinitionList(@RequestParam(defaultValue = "1") Integer pageNum,
			@RequestParam(defaultValue = "10") Integer pageSize, @RequestParam(required = false) String category,
			@RequestParam(required = false) String status, @RequestParam(required = false) String keyword) {
		return processDesignService.getProcessDefinitionPage(pageNum, pageSize, category, status, keyword);
	}

	/**
	 * 查询流程定义的所有版本
	 */
	@GetMapping("/versions")
	public List<ProcessDefinitionEntity> getAllVersionsByKey(@RequestParam @NotBlank String processKey) {
		return processDesignService.getAllVersionsByKey(processKey);
	}

	/**
	 * 查询已部署的流程定义
	 */
	@GetMapping("/deployed")
	public List<ProcessDefinitionEntity> getDeployedProcesses(@RequestParam(required = false) String category) {
		return processDesignService.getDeployedProcesses(category);
	}

	/**
	 * 删除流程定义
	 */
	@PostMapping("/delete")
	public void deleteProcessDefinition(@Valid @RequestBody DeleteProcessReq request) {
		processDesignService.deleteProcessDefinition(request.getId(),
				request.getCascade() != null ? request.getCascade() : false,
				request.getAutoDeployOtherVersionFlag() != null ? request.getAutoDeployOtherVersionFlag() : true);
	}

	/**
	 * 删除流程定义（增强版）
	 */
	@PostMapping("/delete-enhanced")
	public void deleteProcessDefinitionEnhanced(@Valid @RequestBody DeleteProcessEnhancedReq request) {
		processDesignService.deleteProcessDefinitionEnhanced(request.getId(),
				request.getCascade() != null ? request.getCascade() : false,
				request.getAutoDeployOtherVersionFlag() != null ? request.getAutoDeployOtherVersionFlag() : true,
				request.getCompletelyRemoveFromCamundaFlag() != null ? request.getCompletelyRemoveFromCamundaFlag() : false);
	}

	/**
	 * 完全删除流程定义（删除该流程Key的所有版本和相关数据）
	 */
	@PostMapping("/completely-delete")
	public void completelyDeleteProcessDefinition(@Valid @RequestBody CompletelyDeleteProcessReq request) {
		processDesignService.completelyDeleteProcessDefinition(request.getProcessKey(),
				request.getCascade() != null ? request.getCascade() : false);
	}

	/**
	 * 复制流程定义
	 */
	@PostMapping("/copy")
	public ProcessDefinitionDTO copyProcessDefinition(@Valid @RequestBody CopyProcessReq request) {
		return processDesignService.copyProcessDefinition(request.getSourceProcessKey(), request.getNewProcessKey(),
				request.getNewProcessName());
	}

	/**
	 * 验证BPMN XML格式
	 */
	@PostMapping("/validate-bpmn")
	public void validateBpmnXml(@Valid @RequestBody ValidateBpmnReq request) {
		log.info("验证BPMN XML格式请求");
		processDesignService.validateBpmnXml(request.getBpmnXml());
	}

	/**
	 * 获取流程定义的BPMN XML
	 */
	@GetMapping("/bpmn-xml")
	public String getBpmnXml(@RequestParam @NotBlank String processKey) {
		return processDesignService.getBpmnXml(processKey);
	}

	/**
	 * 获取流程定义的SVG图
	 */
	@GetMapping("/diagram-svg")
	public String getDiagramSvg(@RequestParam @NotBlank String processKey) {
		return processDesignService.getDiagramSvg(processKey);
	}

	/**
	 * 获取流程定义的最新版本
	 */
	@GetMapping("/latest-version")
	public ProcessDefinitionDTO getLatestVersionByKey(@RequestParam @NotBlank String processKey) {
		return processDesignService.getLatestVersionByKey(processKey);
	}

	/**
	 * 获取当前部署的流程定义版本
	 */
	@GetMapping("/current-deployed")
	public ProcessDefinitionDTO getCurrentDeployedVersion(@RequestParam @NotBlank String processKey) {
		return processDesignService.getCurrentDeployedVersion(processKey);
	}

	/**
	 * 根据版本号获取流程定义
	 */
	@GetMapping("/by-version")
	public ProcessDefinitionDTO getProcessDefinitionByVersion(@RequestParam @NotBlank String processKey,
			@RequestParam @NotNull Integer version) {
		return processDesignService.getProcessDefinitionByVersion(processKey, version);
	}

	/**
	 * 更新流程定义的设计器配置
	 */
	@PostMapping("/update-designer-config")
	public void updateDesignerConfig(@Valid @RequestBody UpdateDesignerConfigReq request) {
		processDesignService.updateDesignerConfig(request.getProcessKey(), request.getDesignerConfig());
	}

	/**
	 * 获取流程定义的设计器配置
	 */
	@GetMapping("/designer-config")
	public String getDesignerConfig(@RequestParam @NotBlank String processKey) {
		return processDesignService.getDesignerConfig(processKey);
	}

	/**
	 * 更新流程定义的标签
	 */
	@PostMapping("/update-tags")
	public void updateTags(@Valid @RequestBody UpdateTagsReq request) {
		processDesignService.updateTags(request.getProcessKey(), request.getTags());
	}

	/**
	 * 根据标签搜索流程定义
	 */
	@PostMapping("/search-by-tags")
	public List<ProcessDefinitionDTO> searchByTags(@Valid @RequestBody SearchByTagsReq request) {
		return processDesignService.searchByTags(request.getTags());
	}

	/**
	 * 获取所有流程分类
	 */
	@GetMapping("/categories")
	public List<String> getAllCategories() {
		return processDesignService.getAllCategories();
	}

	/**
	 * 批量删除流程定义
	 */
	@PostMapping("/batch-delete")
	public void batchDeleteProcessDefinitions(@Valid @RequestBody BatchDeleteProcessReq request) {
		processDesignService.batchDeleteProcessDefinitions(request.getIds(),
				request.getCascade() != null ? request.getCascade() : false,
				request.getAutoDeployOtherVersionFlag() != null ? request.getAutoDeployOtherVersionFlag() : true);
	}

	/**
	 * 批量删除流程定义（增强版）
	 */
	@PostMapping("/batch-delete-enhanced")
	public void batchDeleteProcessDefinitionsEnhanced(@Valid @RequestBody BatchDeleteProcessEnhancedReq request) {
		processDesignService.batchDeleteProcessDefinitionsEnhanced(request.getIds(),
				request.getCascade() != null ? request.getCascade() : false,
				request.getAutoDeployOtherVersionFlag() != null ? request.getAutoDeployOtherVersionFlag() : true,
				request.getCompletelyRemoveFromCamundaFlag() != null ? request.getCompletelyRemoveFromCamundaFlag() : false);
	}

}
