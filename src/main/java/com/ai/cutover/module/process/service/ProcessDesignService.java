package com.ai.cutover.module.process.service;

import com.ai.cutover.module.process.model.dto.ProcessDefinitionDTO;
import com.ai.cutover.module.process.model.entity.ProcessDefinitionEntity;
import com.ai.cutover.module.process.model.req.CreateProcessReq;
import com.ai.cutover.module.process.model.resp.ProcessDeploymentResp;
import com.mybatisflex.core.paginate.Page;

import java.util.List;

/**
 * 流程设计服务接口
 *
 * <AUTHOR>
 */
public interface ProcessDesignService {

	/**
	 * 创建流程定义
	 * @param request 创建流程定义请求
	 * @return 创建的流程定义DTO
	 */
	ProcessDefinitionDTO createProcessDefinition(CreateProcessReq request);

	/**
	 * 更新流程定义
	 * @param processInfo 流程定义信息
	 * @return 更新的流程定义DTO
	 */
	ProcessDefinitionDTO updateProcessDefinition(ProcessDefinitionEntity processInfo);

	/**
	 * 保存流程设计（BPMN XML）
	 * @param processKey 流程Key
	 * @param bpmnXml BPMN XML内容
	 * @param diagramSvg 流程图SVG（可选）
	 * @return 保存的流程定义DTO
	 */
	ProcessDefinitionDTO saveProcessDesign(String processKey, String bpmnXml, String diagramSvg);

	/**
	 * 部署流程定义到Camunda（部署最新版本）
	 * @param processKey 流程Key
	 * @return 部署结果
	 */
	ProcessDeploymentResp deployProcess(String processKey);

	/**
	 * 重新部署指定版本的流程定义
	 * @param processKey 流程Key
	 * @param version 要部署的版本号
	 * @return 部署结果
	 */
	ProcessDeploymentResp redeployProcessVersion(String processKey, Integer version);

	/**
	 * 取消部署流程定义
	 * @param processKey 流程Key
	 * @param cascade 是否级联删除流程实例
	 */
	void undeployProcess(String processKey, boolean cascade);

	/**
	 * 激活流程定义
	 * @param processKey 流程Key
	 */
	void activateProcess(String processKey);

	/**
	 * 挂起流程定义
	 * @param processKey 流程Key
	 */
	void suspendProcess(String processKey);

	/**
	 * 根据ID查询流程定义
	 * @param id 流程定义ID
	 * @return 流程定义DTO
	 */
	ProcessDefinitionDTO getProcessDefinitionById(Long id);

	/**
	 * 根据流程Key查询流程定义
	 * @param processKey 流程Key
	 * @return 流程定义DTO
	 */
	ProcessDefinitionDTO getProcessDefinitionByKey(String processKey);

	/**
	 * 获取流程定义的最新版本
	 * @param processKey 流程Key
	 * @return 最新版本的流程定义DTO
	 */
	ProcessDefinitionDTO getLatestVersionByKey(String processKey);

	/**
	 * 获取当前部署的流程定义版本
	 * @param processKey 流程Key
	 * @return 当前部署的流程定义DTO，如果没有部署则返回null
	 */
	ProcessDefinitionDTO getCurrentDeployedVersion(String processKey);

	/**
	 * 根据版本号获取流程定义
	 * @param processKey 流程Key
	 * @param version 版本号
	 * @return 指定版本的流程定义DTO
	 */
	ProcessDefinitionDTO getProcessDefinitionByVersion(String processKey, Integer version);

	/**
	 * 分页查询流程定义列表
	 * @param pageNum 页码
	 * @param pageSize 页大小
	 * @param category 流程分类（可选）
	 * @param status 流程状态（可选）
	 * @param keyword 关键词搜索（可选）
	 * @return 分页结果
	 */
	Page<ProcessDefinitionDTO> getProcessDefinitionPage(Integer pageNum, Integer pageSize, String category,
			String status, String keyword);

	/**
	 * 查询指定流程Key的所有版本
	 * @param processKey 流程Key
	 * @return 所有版本列表
	 */
	List<ProcessDefinitionEntity> getAllVersionsByKey(String processKey);

	/**
	 * 查询已部署的流程定义
	 * @param category 流程分类（可选）
	 * @return 已部署的流程定义列表
	 */
	List<ProcessDefinitionEntity> getDeployedProcesses(String category);

	/**
	 * 删除流程定义
	 * @param id 流程定义ID
	 * @param cascade 是否级联删除相关数据
	 * @param autoDeployOtherVersionFlag 删除已部署版本后是否自动部署其他版本
	 */
	void deleteProcessDefinition(Long id, boolean cascade, boolean autoDeployOtherVersionFlag);

	/**
	 * 删除流程定义（增强版）
	 * @param id 流程定义ID
	 * @param cascade 是否级联删除相关数据
	 * @param autoDeployOtherVersionFlag 删除已部署版本后是否自动部署其他版本
	 * @param completelyRemoveFromCamundaFlag 是否完全从Camunda中删除该流程Key的所有版本
	 */
	void deleteProcessDefinitionEnhanced(Long id, boolean cascade, boolean autoDeployOtherVersionFlag, boolean completelyRemoveFromCamundaFlag);

	/**
	 * 完全删除流程定义（删除该流程Key的所有版本和相关数据）
	 * @param processKey 流程Key
	 * @param cascade 是否级联删除流程实例等相关数据
	 */
	void completelyDeleteProcessDefinition(String processKey, boolean cascade);

	/**
	 * 复制流程定义
	 * @param sourceProcessKey 源流程Key
	 * @param newProcessKey 新流程Key
	 * @param newProcessName 新流程名称
	 * @return 复制的流程定义DTO
	 */
	ProcessDefinitionDTO copyProcessDefinition(String sourceProcessKey, String newProcessKey, String newProcessName);

	/**
	 * 验证BPMN XML格式
	 * @param bpmnXml BPMN XML内容
	 */
	void validateBpmnXml(String bpmnXml);

	/**
	 * 获取流程定义的BPMN XML
	 * @param processKey 流程Key
	 * @return BPMN XML内容
	 */
	String getBpmnXml(String processKey);

	/**
	 * 获取流程定义的SVG图
	 * @param processKey 流程Key
	 * @return SVG内容
	 */
	String getDiagramSvg(String processKey);

	/**
	 * 更新流程定义的设计器配置
	 * @param processKey 流程Key
	 * @param designerConfig 设计器配置JSON
	 */
	void updateDesignerConfig(String processKey, String designerConfig);

	/**
	 * 获取流程定义的设计器配置
	 * @param processKey 流程Key
	 * @return 设计器配置JSON
	 */
	String getDesignerConfig(String processKey);

	/**
	 * 更新流程定义的标签
	 * @param processKey 流程Key
	 * @param tags 标签JSON数组
	 */
	void updateTags(String processKey, String tags);

	/**
	 * 根据标签搜索流程定义
	 * @param tags 标签列表
	 * @return 匹配的流程定义列表
	 */
	List<ProcessDefinitionDTO> searchByTags(List<String> tags);

	/**
	 * 获取所有流程分类
	 * @return 分类列表
	 */
	List<String> getAllCategories();

	/**
	 * 批量删除流程定义
	 * @param ids 流程定义ID列表
	 * @param cascade 是否级联删除相关数据
	 * @param autoDeployOtherVersionFlag 删除已部署版本后是否自动部署其他版本
	 */
	void batchDeleteProcessDefinitions(List<Long> ids, boolean cascade, boolean autoDeployOtherVersionFlag);

	/**
	 * 批量删除流程定义（增强版）
	 * @param ids 流程定义ID列表
	 * @param cascade 是否级联删除相关数据
	 * @param autoDeployOtherVersionFlag 删除已部署版本后是否自动部署其他版本
	 * @param completelyRemoveFromCamundaFlag 是否完全从Camunda中删除相关流程Key的所有版本
	 */
	void batchDeleteProcessDefinitionsEnhanced(List<Long> ids, boolean cascade, boolean autoDeployOtherVersionFlag, boolean completelyRemoveFromCamundaFlag);

}
