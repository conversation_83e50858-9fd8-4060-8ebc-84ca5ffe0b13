package com.ai.cutover.module.process.model.resp;

import com.ai.cutover.module.process.model.entity.ProcessDefinitionEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 流程定义响应 用于前端展示，不包含敏感信息
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = ProcessDefinitionEntity.class)
public class ProcessDefinitionResp {

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 流程定义Key
	 */
	private String processKey;

	/**
	 * 流程名称
	 */
	private String processName;

	/**
	 * 流程描述
	 */
	private String description;

	/**
	 * 流程分类
	 */
	private String category;

	/**
	 * 流程版本
	 */
	private Integer version;

	/**
	 * 是否已部署
	 */
	private Boolean isDeployed;

	/**
	 * 流程状态
	 */
	private String status;

	/**
	 * 设计器类型
	 */
	private String designerType;

	/**
	 * 流程标签（JSON数组） 用于多维度标记，如：["审批", "紧急", "常用", "模板"]
	 */
	private String tags;

	/**
	 * 备注
	 */
	private String remarks;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人ID
	 */
	private Long createBy;

	/**
	 * 更新人ID
	 */
	private Long updateBy;

}
