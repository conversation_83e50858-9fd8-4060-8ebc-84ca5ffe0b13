package com.ai.cutover.module.process.model.req;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 删除流程定义请求
 *
 * <AUTHOR>
 */
@Data
public class DeleteProcessReq {

	/**
	 * 流程定义ID
	 */
	@NotNull(message = "流程定义ID不能为空")
	private Long id;

	/**
	 * 是否级联删除相关数据
	 */
	private Boolean cascade;

	/**
	 * 删除已部署版本后是否自动部署其他版本
	 * 默认为true，即自动部署最新可用版本
	 */
	private Boolean autoDeployOtherVersionFlag = true;

	/**
	 * 是否完全从Camunda中删除该流程Key的所有版本
	 * 默认为false，即只删除当前版本的部署
	 */
	private Boolean completelyRemoveFromCamundaFlag = false;

}
