package com.ai.cutover.module.process.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.cutover.common.constant.BusinessConstants;
import com.ai.cutover.common.constant.CommonConstants;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.common.enums.ResourceType;
import com.ai.cutover.common.exception.BusinessException;
import com.ai.cutover.common.util.Assert;
import com.ai.cutover.module.process.constant.ProcessConstants;
import com.ai.cutover.module.process.dao.ProcessDefinitionDao;
import com.ai.cutover.module.process.model.dto.ProcessDefinitionDTO;
import com.ai.cutover.module.process.model.entity.ProcessDefinitionEntity;

import static com.ai.cutover.module.process.model.entity.table.ProcessDefinitionEntityTableDef.PROCESS_DEFINITION_ENTITY;

import com.ai.cutover.module.process.model.req.CreateProcessReq;
import com.ai.cutover.module.process.model.resp.ProcessDeploymentResp;
import com.ai.cutover.module.process.service.ProcessDesignService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.ParseException;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.repository.Deployment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 流程设计服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessDesignServiceImpl implements ProcessDesignService {

	private final ProcessDefinitionDao processDefinitionDao;

	private final RepositoryService repositoryService;

	private final Converter converter;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ProcessDefinitionDTO createProcessDefinition(CreateProcessReq request) {
		// 参数校验
		Assert.hasText(request.getProcessKey(), ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		Assert.hasText(request.getProcessName(), ErrorMessages.Process.PROCESS_NAME_NOT_BLANK);

		// 检查流程Key是否重复
		ProcessDefinitionEntity existingProcess = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(request.getProcessKey())
			.one();
		Assert.isNull(existingProcess, ErrorMessages.Process.PROCESS_KEY_ALREADY_EXISTS);

		// 转换为实体并设置默认值
		ProcessDefinitionEntity processInfo = converter.convert(request, ProcessDefinitionEntity.class);
		processInfo.setVersion(1);
		processInfo.setStatus(ProcessConstants.ProcessDefinitionStatus.DRAFT);
		processInfo.setIsDeployed(false);

		processDefinitionDao.insert(processInfo);
		return converter.convert(processInfo, ProcessDefinitionDTO.class);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ProcessDefinitionDTO updateProcessDefinition(ProcessDefinitionEntity processInfo) {

		// 参数校验
		Assert.notNull(processInfo.getId(), ErrorMessages.Process.PROCESS_DEFINITION_ID_NOT_NULL);

		// 检查流程定义是否存在
		ProcessDefinitionEntity existingProcess = processDefinitionDao.selectOneById(processInfo.getId());
		Assert.resourceExists(existingProcess, ResourceType.PROCESS_DEFINITION);

		// 如果修改了流程Key，检查新Key是否重复
		if (!existingProcess.getProcessKey().equals(processInfo.getProcessKey())) {
			ProcessDefinitionEntity duplicateProcess = QueryChain.of(processDefinitionDao)
				.where(ProcessDefinitionEntity::getProcessKey)
				.eq(processInfo.getProcessKey())
				.one();
			Assert.isNull(duplicateProcess, ErrorMessages.Process.PROCESS_KEY_ALREADY_EXISTS);
		}

		processDefinitionDao.update(processInfo);
		return converter.convert(processInfo, ProcessDefinitionDTO.class);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ProcessDefinitionDTO saveProcessDesign(String processKey, String bpmnXml, String diagramSvg) {

		// 参数校验
		Assert.hasText(processKey, ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		Assert.hasText(bpmnXml, ErrorMessages.Process.BPMN_XML_NOT_BLANK);

		ProcessDefinitionEntity processInfo = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.one();
		Assert.resourceExists(processInfo, ResourceType.PROCESS_DEFINITION);

		// 验证BPMN XML格式（如果无效会抛出异常）
		validateBpmnXml(bpmnXml);

		// 更新BPMN XML和SVG
		processInfo.setBpmnXml(bpmnXml);
		if (diagramSvg != null) {
			processInfo.setDiagramSvg(diagramSvg);
		}

		processDefinitionDao.update(processInfo);
		return converter.convert(processInfo, ProcessDefinitionDTO.class);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ProcessDeploymentResp deployProcess(String processKey) {

		// 1. 获取最新版本的流程定义
		ProcessDefinitionEntity latestProcessInfo = getLatestVersionByProcessKey(processKey);
		Assert.resourceExists(latestProcessInfo, ResourceType.PROCESS_DEFINITION);
		Assert.hasText(latestProcessInfo.getBpmnXml(), ErrorMessages.Process.PROCESS_DEFINITION_MISSING_BPMN);

		// 2. 检查是否需要创建新版本（比较BPMN内容）
		boolean needNewVersion = checkIfNeedNewVersion(processKey, latestProcessInfo.getBpmnXml());

		if (!needNewVersion) {
			// 内容没有变化，检查是否已经部署
			if (latestProcessInfo.getIsDeployed() && StrUtil.isNotBlank(latestProcessInfo.getDeploymentId())
					&& StrUtil.isNotBlank(latestProcessInfo.getCamundaProcessDefinitionId())) {
				// 已经部署且内容相同，直接返回现有部署信息
				log.info("流程定义内容未变化且已部署，直接返回现有部署信息: processKey={}, deploymentId={}", processKey,
						latestProcessInfo.getDeploymentId());
				return new ProcessDeploymentResp(latestProcessInfo.getDeploymentId(),
						latestProcessInfo.getCamundaProcessDefinitionId());
			}
			else {
				// 内容没有变化但未部署，需要部署
				log.info("流程定义内容未变化但尚未部署，执行部署: processKey={}", processKey);
				return deployCurrentVersion(latestProcessInfo);
			}
		}

		// 3. 获取下一个版本号
		Integer nextVersion = getNextVersionNumber(processKey);

		// 4. 部署到Camunda（启用重复过滤）
		Deployment deployment = repositoryService.createDeployment()
			.name(latestProcessInfo.getProcessName() + ProcessConstants.BpmnXml.VERSION_SEPARATOR + nextVersion)
			.addInputStream(processKey + ProcessConstants.BpmnXml.FILE_EXTENSION,
					new ByteArrayInputStream(latestProcessInfo.getBpmnXml().getBytes()))
			.enableDuplicateFiltering(true)
			.deploy();

		// 5. 获取部署后的流程定义
		org.camunda.bpm.engine.repository.ProcessDefinition processDefinition = repositoryService
			.createProcessDefinitionQuery()
			.deploymentId(deployment.getId())
			.singleResult();

		// 6. 检查Camunda是否真的创建了新版本
		if (processDefinition.getVersion() == latestProcessInfo.getVersion()) {
			// Camunda没有创建新版本，说明内容相同，更新现有记录
			return updateExistingDeployment(latestProcessInfo, deployment, processDefinition);
		}

		// 7. 将之前的版本标记为未部署状态
		markPreviousVersionsAsUndeployed(processKey);

		// 8. 创建新版本记录
		ProcessDefinitionEntity newVersionProcess = ProcessDefinitionEntity.builder()
			.processKey(latestProcessInfo.getProcessKey())
			.processName(latestProcessInfo.getProcessName())
			.description(latestProcessInfo.getDescription())
			.category(latestProcessInfo.getCategory())
			.version(processDefinition.getVersion())
			.bpmnXml(latestProcessInfo.getBpmnXml())
			.diagramSvg(latestProcessInfo.getDiagramSvg())
			.isDeployed(true)
			.deploymentId(deployment.getId())
			.camundaProcessDefinitionId(processDefinition.getId())
			.status(ProcessConstants.ProcessDefinitionStatus.ACTIVE)
			.designerType(latestProcessInfo.getDesignerType())
			.designerConfig(latestProcessInfo.getDesignerConfig())
			.tags(latestProcessInfo.getTags())
			.remarks(latestProcessInfo.getRemarks())
			.build();

		processDefinitionDao.insert(newVersionProcess);

		return new ProcessDeploymentResp(deployment.getId(), processDefinition.getId());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ProcessDeploymentResp redeployProcessVersion(String processKey, Integer version) {

		// 1. 获取指定版本的流程定义
		ProcessDefinitionEntity targetVersionProcess = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.and(ProcessDefinitionEntity::getVersion)
			.eq(version)
			.one();
		Assert.resourceExists(targetVersionProcess, ResourceType.PROCESS_DEFINITION);
		Assert.hasText(targetVersionProcess.getBpmnXml(), ErrorMessages.Process.PROCESS_DEFINITION_MISSING_BPMN);

		// 2. 获取下一个版本号
		Integer nextVersion = getNextVersionNumber(processKey);

		// 3. 部署到Camunda
		Deployment deployment = repositoryService.createDeployment()
			.name(targetVersionProcess.getProcessName() + ProcessConstants.BpmnXml.VERSION_SEPARATOR + nextVersion)
			.addInputStream(processKey + ProcessConstants.BpmnXml.FILE_EXTENSION,
					new ByteArrayInputStream(targetVersionProcess.getBpmnXml().getBytes()))
			.deploy();

		// 4. 获取部署后的流程定义
		org.camunda.bpm.engine.repository.ProcessDefinition processDefinition = repositoryService
			.createProcessDefinitionQuery()
			.deploymentId(deployment.getId())
			.singleResult();

		// 5. 将之前的版本标记为未部署状态
		markPreviousVersionsAsUndeployed(processKey);

		// 6. 创建新版本记录（基于指定版本的内容）
		ProcessDefinitionEntity newVersionProcess = ProcessDefinitionEntity.builder()
			.processKey(targetVersionProcess.getProcessKey())
			.processName(targetVersionProcess.getProcessName())
			.description(targetVersionProcess.getDescription())
			.category(targetVersionProcess.getCategory())
			.version(nextVersion)
			.bpmnXml(targetVersionProcess.getBpmnXml())
			.diagramSvg(targetVersionProcess.getDiagramSvg())
			.isDeployed(true)
			.deploymentId(deployment.getId())
			.camundaProcessDefinitionId(processDefinition.getId())
			.status(ProcessConstants.ProcessDefinitionStatus.ACTIVE)
			.designerType(targetVersionProcess.getDesignerType())
			.designerConfig(targetVersionProcess.getDesignerConfig())
			.tags(targetVersionProcess.getTags())
			.remarks("重新部署版本 " + version + " 的内容")
			.build();

		processDefinitionDao.insert(newVersionProcess);

		return new ProcessDeploymentResp(deployment.getId(), processDefinition.getId());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void undeployProcess(String processKey, boolean cascade) {

		// 1. 获取当前部署的版本
		ProcessDefinitionEntity currentDeployedProcess = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.and(ProcessDefinitionEntity::getIsDeployed)
			.eq(true)
			.one();

		if (ObjectUtil.isNull(currentDeployedProcess)) {
			throw new BusinessException(ErrorMessages.Process.PROCESS_NOT_DEPLOYED);
		}

		// 2. 从Camunda删除部署
		repositoryService.deleteDeployment(currentDeployedProcess.getDeploymentId(), cascade);

		// 3. 更新当前部署版本的状态
		currentDeployedProcess.setIsDeployed(false);
		currentDeployedProcess.setDeploymentId(null);
		currentDeployedProcess.setCamundaProcessDefinitionId(null);
		currentDeployedProcess.setStatus(ProcessConstants.ProcessDefinitionStatus.DISABLED);
		processDefinitionDao.update(currentDeployedProcess);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void activateProcess(String processKey) {

		// 获取当前部署的版本
		ProcessDefinitionEntity currentDeployedProcess = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.and(ProcessDefinitionEntity::getIsDeployed)
			.eq(true)
			.one();
		Assert.resourceExists(currentDeployedProcess, ResourceType.PROCESS_DEFINITION);

		if (currentDeployedProcess.getCamundaProcessDefinitionId() != null) {
			repositoryService.activateProcessDefinitionById(currentDeployedProcess.getCamundaProcessDefinitionId());
		}

		currentDeployedProcess.setStatus(ProcessConstants.ProcessDefinitionStatus.ACTIVE);
		processDefinitionDao.update(currentDeployedProcess);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void suspendProcess(String processKey) {

		// 获取当前部署的版本
		ProcessDefinitionEntity currentDeployedProcess = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.and(ProcessDefinitionEntity::getIsDeployed)
			.eq(true)
			.one();
		Assert.resourceExists(currentDeployedProcess, ResourceType.PROCESS_DEFINITION);

		if (currentDeployedProcess.getCamundaProcessDefinitionId() != null) {
			repositoryService.suspendProcessDefinitionById(currentDeployedProcess.getCamundaProcessDefinitionId());
		}

		currentDeployedProcess.setStatus(ProcessConstants.ProcessDefinitionStatus.SUSPENDED);
		processDefinitionDao.update(currentDeployedProcess);
	}

	@Override
	public ProcessDefinitionDTO getProcessDefinitionById(Long id) {
		Assert.notNull(id, ErrorMessages.Process.PROCESS_DEFINITION_ID_NOT_NULL);
		ProcessDefinitionDTO processInfo = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getId)
			.eq(id)
			.oneAs(ProcessDefinitionDTO.class);
		Assert.resourceExists(processInfo, ResourceType.PROCESS_DEFINITION);
		return processInfo;
	}

	@Override
	public ProcessDefinitionDTO getProcessDefinitionByKey(String processKey) {
		Assert.hasText(processKey, ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		// 获取最新版本
		ProcessDefinitionEntity processInfo = getLatestVersionByProcessKey(processKey);
		Assert.resourceExists(processInfo, ResourceType.PROCESS_DEFINITION);
		return converter.convert(processInfo, ProcessDefinitionDTO.class);
	}

	@Override
	public ProcessDefinitionDTO getLatestVersionByKey(String processKey) {
		Assert.hasText(processKey, ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		ProcessDefinitionEntity processInfo = getLatestVersionByProcessKey(processKey);
		Assert.resourceExists(processInfo, ResourceType.PROCESS_DEFINITION);
		return converter.convert(processInfo, ProcessDefinitionDTO.class);
	}

	/**
	 * 获取指定流程Key的最新版本流程定义实体
	 * @param processKey 流程Key
	 * @return 最新版本的流程定义实体
	 */
	private ProcessDefinitionEntity getLatestVersionByProcessKey(String processKey) {
		return QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.orderBy(ProcessDefinitionEntity::getVersion, false)
			.limit(1)
			.one();
	}

	/**
	 * 获取指定流程Key的下一个版本号 优先从Camunda获取，确保版本号一致性
	 * @param processKey 流程Key
	 * @return 下一个版本号
	 */
	private Integer getNextVersionNumber(String processKey) {
		// 1. 先从Camunda获取最新版本号
		org.camunda.bpm.engine.repository.ProcessDefinition latestCamundaProcess = repositoryService
			.createProcessDefinitionQuery()
			.processDefinitionKey(processKey)
			.latestVersion()
			.singleResult();

		if (latestCamundaProcess != null) {
			return latestCamundaProcess.getVersion() + 1;
		}

		// 2. 如果Camunda中没有，从我们的表中获取
		Integer maxVersion = QueryChain.of(processDefinitionDao)
			.select(PROCESS_DEFINITION_ENTITY.VERSION)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.orderBy(ProcessDefinitionEntity::getVersion, false)
			.limit(1)
			.oneAs(Integer.class);

		return maxVersion != null ? maxVersion + 1 : 1;
	}

	/**
	 * 将指定流程Key的所有之前版本标记为未部署状态
	 * @param processKey 流程Key
	 */
	private void markPreviousVersionsAsUndeployed(String processKey) {
		UpdateChain.of(ProcessDefinitionEntity.class)
			.set(ProcessDefinitionEntity::getIsDeployed, false)
			.set(ProcessDefinitionEntity::getStatus, ProcessConstants.ProcessDefinitionStatus.DISABLED)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.and(ProcessDefinitionEntity::getIsDeployed)
			.eq(true)
			.update();
	}

	/**
	 * 检查是否需要创建新版本（比较BPMN内容）
	 * @param processKey 流程Key
	 * @param newBpmnXml 新的BPMN XML内容
	 * @return 是否需要创建新版本
	 */
	private boolean checkIfNeedNewVersion(String processKey, String newBpmnXml) {
		// 获取当前部署的版本
		ProcessDefinitionEntity currentDeployed = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.and(ProcessDefinitionEntity::getIsDeployed)
			.eq(true)
			.one();

		if (currentDeployed == null) {
			// 没有当前部署版本，需要创建新版本
			return true;
		}

		// 比较BPMN内容（去除空白字符后比较）
		String currentBpmn = currentDeployed.getBpmnXml();
		if (currentBpmn == null) {
			return true;
		}

		// 标准化BPMN内容进行比较
		String normalizedCurrent = normalizeBpmnXml(currentBpmn);
		String normalizedNew = normalizeBpmnXml(newBpmnXml);

		return !normalizedCurrent.equals(normalizedNew);
	}

	/**
	 * 标准化BPMN XML内容（去除格式差异）
	 * @param bpmnXml BPMN XML内容
	 * @return 标准化后的内容
	 */
	private String normalizeBpmnXml(String bpmnXml) {
		if (bpmnXml == null) {
			return "";
		}
		// 去除所有空白字符、换行符等格式差异
		return bpmnXml.replaceAll("\\s+", " ").trim();
	}

	/**
	 * 部署当前版本（内容没有变化但未部署时）
	 * @param currentProcess 当前流程定义
	 * @return 部署结果
	 */
	private ProcessDeploymentResp deployCurrentVersion(ProcessDefinitionEntity currentProcess) {
		// 部署到Camunda
		Deployment deployment = repositoryService.createDeployment()
			.name(currentProcess.getProcessName() + ProcessConstants.BpmnXml.VERSION_SEPARATOR
					+ currentProcess.getVersion())
			.addInputStream(currentProcess.getProcessKey() + ProcessConstants.BpmnXml.FILE_EXTENSION,
					new ByteArrayInputStream(currentProcess.getBpmnXml().getBytes()))
			.enableDuplicateFiltering(true)
			.deploy();

		// 获取部署后的流程定义
		org.camunda.bpm.engine.repository.ProcessDefinition processDefinition = repositoryService
			.createProcessDefinitionQuery()
			.deploymentId(deployment.getId())
			.singleResult();

		// 更新当前记录的部署信息
		currentProcess.setIsDeployed(true);
		currentProcess.setDeploymentId(deployment.getId());
		currentProcess.setCamundaProcessDefinitionId(processDefinition.getId());
		currentProcess.setStatus(ProcessConstants.ProcessDefinitionStatus.ACTIVE);
		processDefinitionDao.update(currentProcess);

		return new ProcessDeploymentResp(deployment.getId(), processDefinition.getId());
	}

	/**
	 * 更新现有部署记录（Camunda没有创建新版本时）
	 * @param existingProcess 现有流程定义
	 * @param deployment 新的部署
	 * @param processDefinition Camunda流程定义
	 * @return 部署结果
	 */
	private ProcessDeploymentResp updateExistingDeployment(ProcessDefinitionEntity existingProcess,
			Deployment deployment, org.camunda.bpm.engine.repository.ProcessDefinition processDefinition) {

		// 更新现有记录的部署信息
		existingProcess.setIsDeployed(true);
		existingProcess.setDeploymentId(deployment.getId());
		existingProcess.setCamundaProcessDefinitionId(processDefinition.getId());
		existingProcess.setStatus(ProcessConstants.ProcessDefinitionStatus.ACTIVE);
		processDefinitionDao.update(existingProcess);

		return new ProcessDeploymentResp(deployment.getId(), processDefinition.getId());
	}

	@Override
	public ProcessDefinitionDTO getCurrentDeployedVersion(String processKey) {
		Assert.hasText(processKey, ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		ProcessDefinitionEntity currentDeployedProcess = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.and(ProcessDefinitionEntity::getIsDeployed)
			.eq(true)
			.one();

		return currentDeployedProcess != null ? converter.convert(currentDeployedProcess, ProcessDefinitionDTO.class)
				: null;
	}

	@Override
	public ProcessDefinitionDTO getProcessDefinitionByVersion(String processKey, Integer version) {
		Assert.hasText(processKey, ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		Assert.notNull(version, ErrorMessages.Process.PROCESS_DEFINITION_VERSION_NOT_NULL);

		ProcessDefinitionEntity processInfo = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.and(ProcessDefinitionEntity::getVersion)
			.eq(version)
			.one();
		Assert.resourceExists(processInfo, ResourceType.PROCESS_DEFINITION);

		return converter.convert(processInfo, ProcessDefinitionDTO.class);
	}

	@Override
	public Page<ProcessDefinitionDTO> getProcessDefinitionPage(Integer pageNum, Integer pageSize, String category,
			String status, String keyword) {

		if (ObjectUtil.isNull(pageNum) || pageNum < 1) {
			pageNum = BusinessConstants.DefaultValue.DEFAULT_PAGE_NUM;
		}
		if (ObjectUtil.isNull(pageSize) || pageSize < 1) {
			pageSize = BusinessConstants.DefaultValue.DEFAULT_PAGE_SIZE;
		}
		if (pageSize > BusinessConstants.DefaultValue.MAX_PAGE_SIZE) {
			pageSize = BusinessConstants.DefaultValue.MAX_PAGE_SIZE;
		}

		QueryWrapper wrapper = QueryWrapper.create()
			.where(ProcessDefinitionEntity::getCategory)
			.eq(category, StrUtil.isNotBlank(category))
			.and(ProcessDefinitionEntity::getStatus)
			.eq(status, StrUtil.isNotBlank(status))
			.and(ProcessDefinitionEntity::getProcessName)
			.like(keyword, StrUtil.isNotBlank(keyword))
			.or(ProcessDefinitionEntity::getDescription)
			.like(keyword, StrUtil.isNotBlank(keyword))
			.or(ProcessDefinitionEntity::getProcessKey)
			.like(keyword, StrUtil.isNotBlank(keyword))
			.orderBy(ProcessDefinitionEntity::getCreateTime, false);

		return processDefinitionDao.paginateAs(pageNum, pageSize, wrapper, ProcessDefinitionDTO.class);
	}

	@Override
	public List<ProcessDefinitionEntity> getAllVersionsByKey(String processKey) {
		return QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.orderBy(ProcessDefinitionEntity::getVersion, false)
			.list();
	}

	@Override
	public List<ProcessDefinitionEntity> getDeployedProcesses(String category) {
		QueryChain<ProcessDefinitionEntity> queryChain = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getIsDeployed)
			.eq(true)
			.orderBy(ProcessDefinitionEntity::getCreateTime, false);

		if (StrUtil.isNotBlank(category)) {
			queryChain.and(ProcessDefinitionEntity::getCategory).eq(category);
		}

		return queryChain.list();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteProcessDefinition(Long id, boolean cascade, boolean autoDeployOtherVersionFlag) {
		// 调用完整版本的方法，默认不完全删除Camunda数据
		deleteProcessDefinition(id, cascade, autoDeployOtherVersionFlag, false);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteProcessDefinition(Long id, boolean cascade, boolean autoDeployOtherVersionFlag, boolean completelyRemoveFromCamundaFlag) {
		Assert.notNull(id, ErrorMessages.Process.PROCESS_DEFINITION_ID_NOT_NULL);

		ProcessDefinitionEntity processInfo = processDefinitionDao.selectOneById(id);
		Assert.resourceExists(processInfo, ResourceType.PROCESS_DEFINITION);

		// 1. 获取流程信息并清理Camunda中的部署数据
		boolean isCurrentDeployedVersion = processInfo.getIsDeployed();
		String processKey = processInfo.getProcessKey();
		String deploymentId = processInfo.getDeploymentId();

		if (isCurrentDeployedVersion) {
			// 当前部署版本：通过undeployProcess统一处理
			undeployProcess(processKey, cascade);
		} else if (StrUtil.isNotBlank(deploymentId)) {
			// 历史版本：直接删除对应的部署
			repositoryService.deleteDeployment(deploymentId, cascade);
			log.info("已删除历史版本的Camunda部署: deploymentId={}, processKey={}, version={}",
				deploymentId, processKey, processInfo.getVersion());
		}

		// 2. 删除指定的流程定义版本
		processDefinitionDao.deleteById(id);

		// 3. 检查是否需要完全删除Camunda中的数据
		if (completelyRemoveFromCamundaFlag) {
			// 检查是否还有其他版本存在
			List<ProcessDefinitionEntity> remainingVersions = QueryChain.of(processDefinitionDao)
				.where(ProcessDefinitionEntity::getProcessKey)
				.eq(processKey)
				.list();

			// 如果没有其他版本了，完全删除Camunda中的数据
			if (remainingVersions.isEmpty()) {
				completelyRemoveProcessFromCamunda(processKey, cascade);
			}
		}

		// 4. 如果删除的是已部署版本且启用了自动部署，检查是否需要自动部署其他版本
		if (isCurrentDeployedVersion && autoDeployOtherVersionFlag && !completelyRemoveFromCamundaFlag) {
			handleAutoDeployAfterDeletion(processKey);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ProcessDefinitionDTO copyProcessDefinition(String sourceProcessKey, String newProcessKey,
			String newProcessName) {

		Assert.hasText(sourceProcessKey, ErrorMessages.Process.SOURCE_PROCESS_KEY_NOT_BLANK);
		Assert.hasText(newProcessKey, ErrorMessages.Process.NEW_PROCESS_KEY_NOT_BLANK);
		Assert.hasText(newProcessName, ErrorMessages.Process.NEW_PROCESS_NAME_NOT_BLANK);

		ProcessDefinitionEntity sourceProcess = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(sourceProcessKey)
			.one();
		Assert.resourceExists(sourceProcess, ResourceType.PROCESS_DEFINITION);

		// 检查新流程Key是否重复
		ProcessDefinitionEntity existingProcess = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(newProcessKey)
			.one();
		Assert.isNull(existingProcess, ErrorMessages.Process.PROCESS_KEY_ALREADY_EXISTS);

		// 创建新的流程定义
		ProcessDefinitionEntity newProcess = ProcessDefinitionEntity.builder()
			.processKey(newProcessKey)
			.processName(newProcessName)
			.description(sourceProcess.getDescription())
			.category(sourceProcess.getCategory())
			.version(1)
			.bpmnXml(sourceProcess.getBpmnXml())
			.diagramSvg(sourceProcess.getDiagramSvg())
			.isDeployed(false)
			.status(ProcessConstants.ProcessDefinitionStatus.DRAFT)
			.designerType(sourceProcess.getDesignerType())
			.designerConfig(sourceProcess.getDesignerConfig())
			.tags(sourceProcess.getTags())
			.build();

		processDefinitionDao.insert(newProcess);

		return converter.convert(newProcess, ProcessDefinitionDTO.class);
	}

	@Override
	public void validateBpmnXml(String bpmnXml) {
		Assert.hasText(bpmnXml, ErrorMessages.Process.BPMN_XML_NOT_BLANK);

		String tempDeploymentName = CommonConstants.TempConstants.TEMP_DEPLOYMENT_PREFIX + System.currentTimeMillis();
		Deployment tempDeployment = null;

		try {
			// 创建临时部署进行验证
			tempDeployment = repositoryService.createDeployment()
				.name(tempDeploymentName)
				.addInputStream(CommonConstants.TempConstants.TEMP_VALIDATION_FILE_NAME,
						new ByteArrayInputStream(bpmnXml.getBytes(StandardCharsets.UTF_8)))
				.enableDuplicateFiltering(false)
				.deploy();

			// 如果能成功部署，说明BPMN XML是有效的，方法正常结束

		}
		catch (ParseException parseException) {
			log.error("BPMN XML解析失败", parseException);
			throw new BusinessException(ErrorMessages.Process.BPMN_XML_PARSE_FAILED, parseException.getMessage());
		}
		catch (Exception e) {
			log.error("BPMN XML验证失败", e);
			throw new BusinessException(ErrorMessages.Process.BPMN_XML_INVALID, e.getMessage());
		}
		finally {
			// 清理临时部署
			if (tempDeployment != null) {
				try {
					repositoryService.deleteDeployment(tempDeployment.getId(), true);
				}
				catch (Exception e) {
					log.warn("清理临时部署失败: deploymentId={}", tempDeployment.getId(), e);
				}
			}
		}
	}

	@Override
	public String getBpmnXml(String processKey) {
		Assert.hasText(processKey, ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		// 获取最新版本的BPMN XML
		ProcessDefinitionEntity processInfo = getLatestVersionByProcessKey(processKey);
		Assert.resourceExists(processInfo, ResourceType.PROCESS_DEFINITION);
		return processInfo.getBpmnXml();
	}

	@Override
	public String getDiagramSvg(String processKey) {
		Assert.hasText(processKey, ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		// 获取最新版本的SVG
		ProcessDefinitionEntity processInfo = getLatestVersionByProcessKey(processKey);
		Assert.resourceExists(processInfo, ResourceType.PROCESS_DEFINITION);
		return processInfo.getDiagramSvg();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateDesignerConfig(String processKey, String designerConfig) {
		Assert.hasText(processKey, ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		ProcessDefinitionEntity processInfo = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.one();
		Assert.resourceExists(processInfo, ResourceType.PROCESS_DEFINITION);

		processInfo.setDesignerConfig(designerConfig);
		processDefinitionDao.update(processInfo);
	}

	@Override
	public String getDesignerConfig(String processKey) {
		Assert.hasText(processKey, ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		ProcessDefinitionEntity processInfo = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.one();
		Assert.resourceExists(processInfo, ResourceType.PROCESS_DEFINITION);
		return processInfo.getDesignerConfig();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateTags(String processKey, String tags) {
		Assert.hasText(processKey, ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		ProcessDefinitionEntity processInfo = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.one();
		Assert.resourceExists(processInfo, ResourceType.PROCESS_DEFINITION);

		processInfo.setTags(tags);
		processDefinitionDao.update(processInfo);
	}

	@Override
	public List<ProcessDefinitionDTO> searchByTags(List<String> tags) {
		if (tags == null || tags.isEmpty()) {
			return List.of();
		}

		QueryWrapper wrapper = QueryWrapper.create();

		// 使用JSON_CONTAINS函数进行精确匹配
		for (String tag : tags) {
			wrapper.where("JSON_CONTAINS(tags, ?)", "\"" + tag + "\"");
		}

		wrapper.orderBy(PROCESS_DEFINITION_ENTITY.CREATE_TIME.desc());

		return processDefinitionDao.selectListByQueryAs(wrapper, ProcessDefinitionDTO.class);
	}

	@Override
	public List<String> getAllCategories() {
		return QueryChain.of(processDefinitionDao)
			.select(PROCESS_DEFINITION_ENTITY.CATEGORY)
			.where(PROCESS_DEFINITION_ENTITY.CATEGORY.isNotNull())
			.groupBy(PROCESS_DEFINITION_ENTITY.CATEGORY)
			.orderBy(PROCESS_DEFINITION_ENTITY.CATEGORY.asc())
			.listAs(String.class);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchDeleteProcessDefinitions(List<Long> ids, boolean cascade, boolean autoDeployOtherVersionFlag) {
		// 调用完整版本的方法，默认不完全删除Camunda数据
		batchDeleteProcessDefinitions(ids, cascade, autoDeployOtherVersionFlag, false);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchDeleteProcessDefinitions(List<Long> ids, boolean cascade, boolean autoDeployOtherVersionFlag, boolean completelyRemoveFromCamundaFlag) {
		Assert.notEmpty(ids, ErrorMessages.Common.PARAM_MISSING);

		// 1. 批量查询需要删除的流程定义
		List<ProcessDefinitionEntity> processesToDelete = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getId)
			.in(ids)
			.list();

		// 2. 清理所有流程定义的Camunda部署数据
		for (ProcessDefinitionEntity processInfo : processesToDelete) {
			boolean isCurrentDeployedVersion = processInfo.getIsDeployed();
			String processKey = processInfo.getProcessKey();
			String deploymentId = processInfo.getDeploymentId();

			if (isCurrentDeployedVersion) {
				// 当前部署版本：通过undeployProcess统一处理
				undeployProcess(processKey, cascade);
			} else if (StrUtil.isNotBlank(deploymentId)) {
				// 历史版本：直接删除对应的部署
				repositoryService.deleteDeployment(deploymentId, cascade);
				log.info("已删除历史版本的Camunda部署: deploymentId={}, processKey={}, version={}",
					deploymentId, processKey, processInfo.getVersion());
			}
		}

		// 3. 收集已部署的流程Key（用于后续自动部署逻辑）
		List<String> deployedProcessKeys = processesToDelete.stream()
			.filter(ProcessDefinitionEntity::getIsDeployed)
			.map(ProcessDefinitionEntity::getProcessKey)
			.distinct()
			.toList();

		// 4. 批量删除流程定义
		UpdateChain.of(ProcessDefinitionEntity.class).where(ProcessDefinitionEntity::getId).in(ids).remove();

		// 5. 如果需要完全删除Camunda数据，检查每个流程Key是否还有剩余版本
		if (completelyRemoveFromCamundaFlag) {
			processesToDelete.stream()
				.map(ProcessDefinitionEntity::getProcessKey)
				.distinct()
				.forEach(processKey -> {
					// 检查该流程Key是否还有其他版本存在
					List<ProcessDefinitionEntity> remainingVersions = QueryChain.of(processDefinitionDao)
						.where(ProcessDefinitionEntity::getProcessKey)
						.eq(processKey)
						.list();

					// 如果没有其他版本了，完全删除Camunda中的数据
					if (remainingVersions.isEmpty()) {
						completelyRemoveProcessFromCamunda(processKey, cascade);
					}
				});
		}

		// 6. 如果启用了自动部署且不是完全删除模式，对于删除了已部署版本的流程，尝试自动部署其他版本
		if (autoDeployOtherVersionFlag && !completelyRemoveFromCamundaFlag) {
			deployedProcessKeys.forEach(processKey -> handleAutoDeployAfterDeletion(processKey));
		}
	}

	/**
	 * 删除已部署版本后的自动部署处理 如果还有其他可用版本，自动部署最新版本
	 * @param processKey 流程Key
	 */
	private void handleAutoDeployAfterDeletion(String processKey) {
		// 1. 查找该流程Key下的最新可用版本
		ProcessDefinitionEntity latestAvailableVersion = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.orderBy(ProcessDefinitionEntity::getVersion, false)
			.one();

		// 2. 如果还有其他版本存在，自动部署最新版本
		if (ObjectUtil.isNotNull(latestAvailableVersion)) {
			try {
				// 部署最新版本
				deployProcessByVersion(processKey, latestAvailableVersion.getVersion());
				log.info("已自动部署流程 [{}] 的最新版本 [{}]", processKey, latestAvailableVersion.getVersion());
			}
			catch (Exception e) {
				// 自动部署失败时记录日志，但不抛出异常，避免影响删除操作
				log.warn("自动部署流程 [{}] 的最新版本 [{}] 失败: {}", processKey, latestAvailableVersion.getVersion(),
						e.getMessage());
			}
		}
		else {
			log.info("流程 [{}] 的所有版本已删除，无需自动部署", processKey);
		}
	}

	/**
	 * 部署指定版本的流程定义
	 * @param processKey 流程Key
	 * @param version 版本号
	 */
	private void deployProcessByVersion(String processKey, Integer version) {
		// 1. 参数校验
		Assert.hasText(processKey, ErrorMessages.Process.PROCESS_KEY_NOT_BLANK);
		Assert.notNull(version, ErrorMessages.Process.PROCESS_DEFINITION_VERSION_NOT_NULL);

		// 2. 获取指定版本的流程定义
		ProcessDefinitionEntity processDefinitionEntity = QueryChain.of(processDefinitionDao)
			.where(ProcessDefinitionEntity::getProcessKey)
			.eq(processKey)
			.and(ProcessDefinitionEntity::getVersion)
			.eq(version)
			.one();

		Assert.resourceExists(processDefinitionEntity, ResourceType.PROCESS_DEFINITION);

		// 3. 验证BPMN XML
		validateBpmnXml(processDefinitionEntity.getBpmnXml());

		// 4. 部署到Camunda
		String deploymentName = processDefinitionEntity.getProcessName() + ProcessConstants.BpmnXml.VERSION_SEPARATOR
				+ processDefinitionEntity.getVersion();

		Deployment deployment = repositoryService.createDeployment()
			.name(deploymentName)
			.addString(processDefinitionEntity.getProcessKey() + ProcessConstants.BpmnXml.FILE_EXTENSION,
					processDefinitionEntity.getBpmnXml())
			.deploy();

		// 5. 获取Camunda流程定义ID
		org.camunda.bpm.engine.repository.ProcessDefinition camundaProcessDefinition = repositoryService
			.createProcessDefinitionQuery()
			.deploymentId(deployment.getId())
			.singleResult();

		// 6. 更新流程定义状态
		processDefinitionEntity.setIsDeployed(true);
		processDefinitionEntity.setDeploymentId(deployment.getId());
		processDefinitionEntity.setCamundaProcessDefinitionId(camundaProcessDefinition.getId());
		processDefinitionEntity.setStatus(ProcessConstants.ProcessDefinitionStatus.ACTIVE);
		processDefinitionDao.update(processDefinitionEntity);
	}

	/**
	 * 完全从Camunda中删除指定流程Key的所有相关数据
	 * @param processKey 流程Key
	 * @param cascade 是否级联删除流程实例等相关数据
	 */
	private void completelyRemoveProcessFromCamunda(String processKey, boolean cascade) {
		try {
			// 1. 查询Camunda中该流程Key的所有流程定义
			List<org.camunda.bpm.engine.repository.ProcessDefinition> camundaProcessDefinitions = repositoryService
				.createProcessDefinitionQuery()
				.processDefinitionKey(processKey)
				.list();

			if (camundaProcessDefinitions.isEmpty()) {
				log.info("Camunda中不存在流程Key为 [{}] 的流程定义", processKey);
				return;
			}

			// 2. 收集所有部署ID
			Set<String> deploymentIds = camundaProcessDefinitions.stream()
				.map(org.camunda.bpm.engine.repository.ProcessDefinition::getDeploymentId)
				.collect(Collectors.toSet());

			// 3. 删除所有相关的部署
			for (String deploymentId : deploymentIds) {
				repositoryService.deleteDeployment(deploymentId, cascade);
				log.info("已删除Camunda部署: deploymentId={}, processKey={}", deploymentId, processKey);
			}

			log.info("已完全从Camunda中删除流程Key [{}] 的所有数据，删除部署数量: {}", processKey, deploymentIds.size());

		} catch (Exception e) {
			log.error("完全删除Camunda流程数据失败: processKey={}", processKey, e);
			// 不抛出异常，避免影响主要的删除流程
		}
	}

}
