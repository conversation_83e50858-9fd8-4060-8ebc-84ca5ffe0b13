package com.ai.cutover.common.service;

import com.ai.cutover.common.exception.BusinessException;
import com.ai.cutover.module.system.model.entity.SysDeptEntity;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis服务测试类 验证缓存操作和异常处理的正确性
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("dev")
public class RedisServiceTest {

	@Autowired
	private RedisService redisService;

	/**
	 * 测试基本的缓存操作
	 */
	@Test
	public void testBasicCacheOperations() {
		String key = "test:basic:key";
		String value = "test_value";

		// 测试设置和获取
		redisService.set(key, value);
		Object result = redisService.get(key);
		assertEquals(value, result);

		// 测试类型转换
		String stringResult = redisService.get(key, String.class);
		assertEquals(value, stringResult);

		// 测试删除
		Boolean deleted = redisService.delete(key);
		assertTrue(deleted);

		// 验证删除后获取为null
		Object afterDelete = redisService.get(key);
		assertNull(afterDelete);
	}

	/**
	 * 测试数值类型转换（Java 17 switch表达式）
	 */
	@Test
	public void testNumberTypeConversion() {
		String key = "test:number:key";

		// 测试Long类型
		redisService.set(key, 123L);
		Long longResult = redisService.get(key, Long.class);
		assertEquals(123L, longResult);

		// 测试Integer类型
		redisService.set(key, 456);
		Integer intResult = redisService.get(key, Integer.class);
		assertEquals(456, intResult);

		// 测试Double类型
		redisService.set(key, 78.9);
		Double doubleResult = redisService.get(key, Double.class);
		assertEquals(78.9, doubleResult);

		// 清理
		redisService.delete(key);
	}

	/**
	 * 测试布尔类型转换（Java 17 instanceof模式匹配）
	 */
	@Test
	public void testBooleanTypeConversion() {
		String key = "test:boolean:key";

		// 测试数值到布尔的转换
		redisService.set(key, 1);
		Boolean boolFromNumber = redisService.get(key, Boolean.class);
		assertTrue(boolFromNumber);

		redisService.set(key, 0);
		boolFromNumber = redisService.get(key, Boolean.class);
		assertFalse(boolFromNumber);

		// 测试字符串到布尔的转换
		redisService.set(key, "true");
		Boolean boolFromString = redisService.get(key, Boolean.class);
		assertTrue(boolFromString);

		redisService.set(key, "false");
		boolFromString = redisService.get(key, Boolean.class);
		assertFalse(boolFromString);

		// 测试基本类型boolean
		redisService.set(key, true);
		boolean primitiveBool = redisService.get(key, boolean.class);
		assertTrue(primitiveBool);

		// 清理
		redisService.delete(key);
	}

	/**
	 * 测试类型转换异常
	 */
	@Test
	public void testTypeConversionException() {
		String key = "test:conversion:exception";

		// 设置一个复杂对象
		redisService.set(key, new SysDeptEntity());

		// 尝试转换为不兼容的类型应该抛出BusinessException
		assertThrows(BusinessException.class, () -> {
			redisService.get(key, Integer.class);
		});

		// 清理
		redisService.delete(key);
	}

	/**
	 * 测试异常处理（BusinessException）
	 */
	@Test
	public void testExceptionHandling() {
		// 注意：这个测试需要在Redis不可用的情况下运行
		// 在正常情况下，Redis操作应该成功
		// 这里我们主要验证异常类型是否正确

		String key = "test:exception:key";
		String value = "test_value";

		// 正常操作应该不抛出异常
		assertDoesNotThrow(() -> {
			redisService.set(key, value);
			redisService.get(key);
			redisService.delete(key);
		});
	}

	/**
	 * 测试键存在性检查
	 */
	@Test
	public void testKeyExistence() {
		String key = "test:existence:key";
		String value = "test_value";

		// 初始状态键不存在
		Boolean exists = redisService.hasKey(key);
		assertFalse(exists);

		// 设置值后键存在
		redisService.set(key, value);
		exists = redisService.hasKey(key);
		assertTrue(exists);

		// 删除后键不存在
		redisService.delete(key);
		exists = redisService.hasKey(key);
		assertFalse(exists);
	}

	/**
	 * 测试递增递减操作
	 */
	@Test
	public void testIncrementDecrement() {
		String key = "test:counter:key";

		// 测试递增
		Long result = redisService.increment(key);
		assertEquals(1L, result);

		result = redisService.increment(key, 5);
		assertEquals(6L, result);

		// 测试递减
		result = redisService.decrement(key);
		assertEquals(5L, result);

		result = redisService.decrement(key, 3);
		assertEquals(2L, result);

		// 清理
		redisService.delete(key);
	}

}
