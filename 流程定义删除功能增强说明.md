# 流程定义删除功能增强说明

## 问题描述

原有的流程定义删除功能存在以下问题：
1. 删除流程定义时，只会删除我们自己的流程定义表的数据
2. 不会删除Camunda流程定义表中的数据
3. 如果该流程Key在Camunda中还有其他历史版本，这些版本不会被清理

## 解决方案

### 1. 新增接口方法

#### ProcessDesignService接口新增方法：
- `deleteProcessDefinitionEnhanced()` - 删除流程定义增强版
- `completelyDeleteProcessDefinition()` - 完全删除流程定义
- `batchDeleteProcessDefinitionsEnhanced()` - 批量删除流程定义增强版

#### ProcessDesignController新增接口：
- `POST /api/process-design/delete-enhanced` - 删除流程定义增强版
- `POST /api/process-design/completely-delete` - 完全删除流程定义
- `POST /api/process-design/batch-delete-enhanced` - 批量删除流程定义增强版

### 2. 新增请求类

#### DeleteProcessEnhancedReq
```java
@Data
public class DeleteProcessEnhancedReq {
    @NotNull(message = "流程定义ID不能为空")
    private Long id;
    private Boolean cascade;
    private Boolean autoDeployOtherVersionFlag = true;
    private Boolean completelyRemoveFromCamundaFlag = false; // 新增字段
}
```

#### CompletelyDeleteProcessReq
```java
@Data
public class CompletelyDeleteProcessReq {
    @NotBlank(message = "流程Key不能为空")
    private String processKey;
    private Boolean cascade = false;
}
```

#### BatchDeleteProcessEnhancedReq
```java
@Data
public class BatchDeleteProcessEnhancedReq {
    @NotEmpty(message = "流程定义ID列表不能为空")
    private List<Long> ids;
    private Boolean cascade;
    private Boolean autoDeployOtherVersionFlag = true;
    private Boolean completelyRemoveFromCamundaFlag = false; // 新增字段
}
```

### 3. 核心功能实现

#### 3.1 增强版删除方法
`deleteProcessDefinitionEnhanced()` 方法增加了 `completelyRemoveFromCamundaFlag` 参数：
- 当为 `true` 时，如果删除后该流程Key没有其他版本，会完全清理Camunda中的所有相关数据
- 当为 `false` 时，保持原有行为

#### 3.2 完全删除方法
`completelyDeleteProcessDefinition()` 方法会：
1. 删除我们表中该流程Key的所有版本
2. 完全清理Camunda中该流程Key的所有相关数据

#### 3.3 核心清理方法
`completelyRemoveProcessFromCamunda()` 私有方法：
1. 查询Camunda中该流程Key的所有流程定义
2. 收集所有相关的部署ID
3. 删除所有相关的部署
4. 支持级联删除流程实例等相关数据

### 4. 使用场景

#### 场景1：普通删除（保持原有行为）
```http
POST /api/process-design/delete
{
    "id": 123,
    "cascade": false,
    "autoDeployOtherVersionFlag": true
}
```

#### 场景2：删除时清理Camunda历史数据
```http
POST /api/process-design/delete-enhanced
{
    "id": 123,
    "cascade": false,
    "autoDeployOtherVersionFlag": true,
    "completelyRemoveFromCamundaFlag": true
}
```

#### 场景3：完全删除流程定义
```http
POST /api/process-design/completely-delete
{
    "processKey": "myProcess",
    "cascade": false
}
```

### 5. 向后兼容性

- 原有的删除接口保持不变，确保向后兼容
- 原有的删除方法内部调用增强版方法，默认不完全删除Camunda数据
- 新增的功能通过新的接口和参数提供

### 6. 安全性考虑

- 完全删除功能会记录详细日志
- 删除Camunda数据时使用try-catch，避免影响主要删除流程
- 支持级联删除参数，可控制是否删除流程实例等相关数据

### 7. 日志记录

增强了日志记录功能：
- 记录删除的部署数量
- 记录删除失败的情况
- 提供详细的操作追踪信息

## 总结

通过这次增强，解决了原有删除功能不完整的问题，提供了更灵活的删除选项：
1. 保持原有功能的向后兼容性
2. 提供完全清理Camunda数据的选项
3. 支持批量操作
4. 增强了错误处理和日志记录
