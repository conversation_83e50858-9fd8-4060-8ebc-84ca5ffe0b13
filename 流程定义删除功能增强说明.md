# 流程定义删除功能增强说明

## 问题描述

原有的流程定义删除功能存在以下问题：
1. 删除流程定义时，只会删除我们自己的流程定义表的数据
2. 不会删除Camunda流程定义表中的数据
3. 如果该流程Key在Camunda中还有其他历史版本，这些版本不会被清理

## 解决方案

### 1. 统一删除接口

#### ProcessDesignService接口方法：
- `deleteProcessDefinition()` - 统一的删除流程定义方法（增加了completelyRemoveFromCamundaFlag参数）
- `batchDeleteProcessDefinitions()` - 统一的批量删除流程定义方法（增加了completelyRemoveFromCamundaFlag参数）

#### ProcessDesignController接口：
- `POST /api/process-design/delete` - 删除流程定义（统一接口）
- `POST /api/process-design/batch-delete` - 批量删除流程定义（统一接口）

### 2. 增强的请求类

#### DeleteProcessReq
```java
@Data
public class DeleteProcessReq {
    @NotNull(message = "流程定义ID不能为空")
    private Long id;
    private Boolean cascade;
    private Boolean autoDeployOtherVersionFlag = true;
    private Boolean completelyRemoveFromCamundaFlag = false; // 新增字段
}
```

#### BatchDeleteProcessReq
```java
@Data
public class BatchDeleteProcessReq {
    @NotEmpty(message = "流程定义ID列表不能为空")
    private List<Long> ids;
    private Boolean cascade;
    private Boolean autoDeployOtherVersionFlag = true;
    private Boolean completelyRemoveFromCamundaFlag = false; // 新增字段
}
```

### 3. 核心功能实现

#### 3.1 统一删除方法
`deleteProcessDefinition()` 方法增加了 `completelyRemoveFromCamundaFlag` 参数：
- 当为 `true` 时，如果删除后该流程Key没有其他版本，会完全清理Camunda中的所有相关数据
- 当为 `false` 时，保持原有行为

#### 3.2 核心清理方法
`completelyRemoveProcessFromCamunda()` 私有方法：
1. 查询Camunda中该流程Key的所有流程定义
2. 收集所有相关的部署ID
3. 删除所有相关的部署
4. 支持级联删除流程实例等相关数据

### 4. 使用场景

#### 场景1：普通删除（保持原有行为）
```http
POST /api/process-design/delete
{
    "id": 123,
    "cascade": false,
    "autoDeployOtherVersionFlag": true,
    "completelyRemoveFromCamundaFlag": false
}
```

#### 场景2：删除时清理Camunda历史数据
```http
POST /api/process-design/delete
{
    "id": 123,
    "cascade": false,
    "autoDeployOtherVersionFlag": true,
    "completelyRemoveFromCamundaFlag": true
}
```

#### 场景3：批量删除并清理Camunda数据
```http
POST /api/process-design/batch-delete
{
    "ids": [123, 124, 125],
    "cascade": false,
    "autoDeployOtherVersionFlag": true,
    "completelyRemoveFromCamundaFlag": true
}
```

### 5. 向后兼容性

- 原有的删除接口保持不变，确保向后兼容
- 新增的 `completelyRemoveFromCamundaFlag` 参数默认为 `false`，保持原有行为
- 通过新增参数提供增强功能，不影响现有调用

### 6. 安全性考虑

- 完全删除功能会记录详细日志
- 删除Camunda数据时使用try-catch，避免影响主要删除流程
- 支持级联删除参数，可控制是否删除流程实例等相关数据

### 7. 日志记录

增强了日志记录功能：
- 记录删除的部署数量
- 记录删除失败的情况
- 提供详细的操作追踪信息

## 总结

通过这次优化，将删除功能统一到一个接口中：
1. **简化接口**：只保留原有的删除接口，通过新增参数提供增强功能
2. **向后兼容**：原有调用方式完全不受影响
3. **功能增强**：通过 `completelyRemoveFromCamundaFlag` 参数控制是否完全清理Camunda数据
4. **统一管理**：所有删除逻辑集中在一个方法中，便于维护

### 核心参数说明

- `completelyRemoveFromCamundaFlag = false`：保持原有行为，只删除当前部署
- `completelyRemoveFromCamundaFlag = true`：如果删除后该流程Key没有其他版本，完全清理Camunda中的所有相关数据
